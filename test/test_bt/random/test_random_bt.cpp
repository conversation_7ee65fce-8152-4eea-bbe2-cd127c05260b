#include "action_node.hpp"
#include "condition_node.hpp"
#include "random_mower.hpp"
#include "velocity_publisher.hpp"

using namespace BT;

struct Context
{
    int counter = 0;
    double value = 0.0;
    std::string status = "idle";
    std::shared_ptr<fescue_iox::NavigationRandomMowerAlg> random_alg = nullptr;
};

class InitRandomMower : public fescue_iox::ActionNode<Context>
{
public:
    InitRandomMower(const std::string& name, const BT::NodeConfig& config)
        : fescue_iox::ActionNode<Context>(name, config)
    {
    }

    void OnStart() override
    {
        std::cout << "Init random mower start" << std::endl;
        auto context = std::make_shared<Context>();
        context->counter = 1;
        context->value = 3;
        context->status = "initialized";
        fescue_iox::RandomMowerAlgParam param;
        context->random_alg = std::make_shared<fescue_iox::NavigationRandomMowerAlg>(param);
        context->random_alg->SetFeatureSelectCallback([](const std::vector<fescue_iox::FeatureSelectData>& data) { (void)data; });
        context->random_alg->SetRandomMowerStateCallback([](fescue_iox::RandomMowerRunningState state) -> void {
            (void)state;
        });
        context->random_alg->SetPubExceptionCallback([](mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value) {
            (void)level;
            (void)value;
        });
        setOutput("context_ptr", context);
    }

    BT::NodeStatus OnRunning() override
    {
        return BT::NodeStatus::SUCCESS;
    }
};

class IsReadyToMowing : public fescue_iox::ConditionNode<Context>
{
public:
    IsReadyToMowing(const std::string& name, const BT::NodeConfig& config)
        : fescue_iox::ConditionNode<Context>(name, config)
    {
    }

    BT::NodeStatus OnTick() override
    {
        auto& context_data = context();
        std::cout << "IsReadyToMowing: " << context_data.status 
                  << " value: " << context_data.value
                  << std::endl;
        return BT::NodeStatus::SUCCESS;
    }
};

class RandomMowing : public fescue_iox::ActionNode<Context>
{
public:
    RandomMowing(const std::string& name, const BT::NodeConfig& config)
        : fescue_iox::ActionNode<Context>(name, config)
    {
    }

    void OnStart() override
    {
    }

    BT::NodeStatus OnRunning() override
    {
        auto& context_data = context();
        auto running_state = context_data.random_alg->GetMowerRunningState();
        context_data.counter++;
        std::cout << "Mowing randomly running_state: " << static_cast<int>(running_state)
                  << " counter: " << context_data.counter
                  << " value: " << context_data.value
                  << " status: " << context_data.status
                  << std::endl;
        return BT::NodeStatus::RUNNING;
    }
};

int main(int argc, char** argv)
{
    if (argc != 2) {
        std::cerr << "Usage: " << argv[0] << " <xml_file>" << std::endl;
        return 1;
    }

    BehaviorTreeFactory factory;
    factory.registerNodeType<InitRandomMower>("InitRandomMower");
    factory.registerNodeType<RandomMowing>("RandomMowing");
    factory.registerNodeType<IsReadyToMowing>("IsReadyToMowing");
    std::string xml_file = argv[1];
    auto tree = factory.createTreeFromFile(xml_file);
    tree.tickWhileRunning();

    return 0;
}