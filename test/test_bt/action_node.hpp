#pragma once

#include <memory>
#include <string>

#include "behaviortree_cpp/bt_factory.h"

namespace fescue_iox
{

template <typename T>
class ActionNode : public BT::StatefulActionNode
{
    using ContextPtr = std::shared_ptr<T>;

public:
    ActionNode(const std::string& name, const BT::NodeConfig& config)
        : BT::StatefulActionNode(name, config)
    {
    }

    virtual void OnStart() = 0;
    virtual BT::NodeStatus OnRunning() = 0;

    BT::NodeStatus onStart() override
    {
        OnStart();
        return BT::NodeStatus::RUNNING;
    }

    BT::NodeStatus onRunning() override
    {
        return OnRunning();
    }

    void onHalted() override
    {
    }

    static BT::PortsList providedPorts()
    {
        return {
            BT::BidirectionalPort<ContextPtr>("context_ptr")};
    }

protected:
    T& context()
    {
        auto key = getInput<ContextPtr>("context_ptr");
        if (!key) {
            throw std::runtime_error("Failed to get context_ptr in " + this->name() + " : " + key.error());
        }
        auto context_ptr = key.value();
        return *context_ptr;
    }
};

} // namespace fescue_iox