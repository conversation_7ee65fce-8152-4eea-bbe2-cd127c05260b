cmake_minimum_required(VERSION 3.16)
project(test_bt C CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O3")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG}  -fPIC -g")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -O3")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fPIC -g")
set(CMAKE_BUILD_TYPE "Release")

find_package(OpenCV REQUIRED)
# find_package(iceoryx_binding_c REQUIRED)
# find_package(catkin REQUIRED COMPONENTS behaviortree_cpp)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/
    ${CMAKE_SOURCE_DIR}/thirdparty/json/include/
    ${CMAKE_SOURCE_DIR}/thirdparty/math/eigen3/
    ${CMAKE_SOURCE_DIR}/common/utils/include/
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_random_mower/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_cross_region/include
    ${CMAKE_SOURCE_DIR}/fescue_msgs/header/
    ${CMAKE_SOURCE_DIR}/mower_msgs/
    ${CMAKE_SOURCE_DIR}/thirdparty/iceoryx/include/
    ${CMAKE_SOURCE_DIR}/thirdparty/behaviortree_cpp/include/
)

add_executable(test_random_bt ${CMAKE_CURRENT_SOURCE_DIR}/random/test_random_bt.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/data_error_detector.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/process_fusion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/pure_pursuit_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_pose_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/multi_trajectory_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_trajectory_generator.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/obstacle_detector.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_random_mower/src/random_mower.cpp
)
target_link_libraries(test_random_bt
    pthread
    opencv_highgui
    opencv_imgcodecs
    opencv_imgproc
    opencv_core
    rt
    dl
    ob_utils
    iceoryx_platform
    iceoryx_posh
    iceoryx_hoofs
    ${CMAKE_SOURCE_DIR}/thirdparty/behaviortree_cpp/lib/${HOST_PLATFORM}/${SOC_NAME}/libbehaviortree_cpp.a
)

# Cross Region Behavior Tree Test
add_executable(test_cross_region_bt ${CMAKE_CURRENT_SOURCE_DIR}/cross_region/test_cross_region_bt.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/data_error_detector.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/process_fusion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/pure_pursuit_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_pose_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/multi_trajectory_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_trajectory_generator.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/obstacle_detector.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/imu_data_processor.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/velocity_publisher.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/velocity_smooth.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/perception_status_filter.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/obstacle_classification.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_cross_region/src/cross_region.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_cross_region/src/cross_region_config.cpp
)
target_link_libraries(test_cross_region_bt
    pthread
    opencv_highgui
    opencv_imgcodecs
    opencv_imgproc
    opencv_core
    rt
    dl
    ob_utils
    attr
    acl
    iceoryx_platform
    iceoryx_posh
    iceoryx_hoofs
    spdlog
    backtrace
    ${CMAKE_SOURCE_DIR}/thirdparty/behaviortree_cpp/lib/${HOST_PLATFORM}/${SOC_NAME}/libbehaviortree_cpp.a
    ${CMAKE_SOURCE_DIR}/thirdparty/yaml-cpp/lib/${HOST_PLATFORM}/${SOC_NAME}/libyaml-cpp.a
)