#pragma once

#include "behaviortree_cpp/bt_factory.h"

#include <memory>
#include <string>

namespace fescue_iox
{

template <typename T>
class ConditionNode : public BT::ConditionNode
{
    using ContextPtr = std::shared_ptr<T>;

public:
    ConditionNode(const std::string& name, const BT::NodeConfig& config)
        : BT::ConditionNode(name, config)
    {
    }

    virtual BT::NodeStatus OnTick() = 0;

    static BT::PortsList providedPorts()
    {
        return {
            BT::BidirectionalPort<ContextPtr>("context_ptr")};
    }

protected:
    T& context()
    {
        auto key = getInput<ContextPtr>("context_ptr");
        if (!key) {
            throw std::runtime_error("Failed to get context_ptr in " + this->name() + " : " + key.error());
        }
        auto context_ptr = key.value();
        return *context_ptr;
    }

private:
    BT::NodeStatus tick() override
    {
        return OnTick();
    }
};

} // namespace fescue_iox