#pragma once

#include "cross_region_context.hpp"
#include "simple_bt_framework.hpp"
#include "simulation_data_generator.hpp"

#include <iostream>
#include <memory>

namespace fescue_iox
{

/**
 * @brief 简化版初始化跨区域算法节点
 */
class SimpleInitCrossRegion : public SimpleBT::ActionNode
{
public:
    SimpleInitCrossRegion(const std::string& name, std::shared_ptr<CrossRegionContext> context)
        : SimpleBT::ActionNode(name)
        , context_(context)
        , data_generator_(std::make_unique<SimulationDataGenerator>())
    {
    }

protected:
    void onStart() override
    {
        if (!context_) {
            std::cerr << "[" << name() << "] Context is null!" << std::endl;
            return;
        }

        std::lock_guard<std::mutex> lock(context_->data_mutex);

        if (context_->enable_debug_output) {
            std::cout << "[" << name() << "] Starting initialization..." << std::endl;
        }

        try {
            // 创建跨区域算法实例
            context_->cross_region_alg = std::make_shared<NavigationCrossRegionAlg>(context_->alg_param);

            // 设置回调函数
            context_->cross_region_alg->SetFeatureSelectCallback([this](const std::vector<FeatureSelectData>& data) {
                (void)data;
                if (context_->enable_debug_output) {
                    std::cout << "[Callback] Feature select callback triggered" << std::endl;
                }
            });

            context_->cross_region_alg->SetCrossRegionRunningStateCallback([this](CrossRegionRunningState state) {
                context_->current_state = state;
                if (context_->enable_debug_output) {
                    std::cout << "[Callback] Cross region state changed to: " << asStringLiteral(state) << std::endl;
                }
            });

            context_->cross_region_alg->SetMarkLocationMarkIdCallback([this](int mark_id) -> bool {
                if (context_->enable_debug_output) {
                    std::cout << "[Callback] Mark location mark id: " << mark_id << std::endl;
                }
                return true;
            });

            // 设置算法运行状态
            context_->mower_state = MowerRunningState::RUNNING;
            context_->cross_region_alg->SetAlgoRunningState(context_->mower_state);

            // 初始化仿真数据
            data_generator_->UpdateSimulationData(*context_, 0.0);

            context_->is_initialized.store(true);
            context_->simulation_running.store(true);
            context_->start_time = std::chrono::steady_clock::now();

            if (context_->enable_debug_output) {
                std::cout << "[" << name() << "] Initialization completed successfully" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "[" << name() << "] Initialization failed: " << e.what() << std::endl;
            context_->is_initialized.store(false);
        }
    }

    SimpleBT::NodeStatus onRunning() override
    {
        if (!context_)
            return SimpleBT::NodeStatus::FAILURE;

        return context_->is_initialized.load() ? SimpleBT::NodeStatus::SUCCESS : SimpleBT::NodeStatus::FAILURE;
    }

private:
    std::shared_ptr<CrossRegionContext> context_;
    std::unique_ptr<SimulationDataGenerator> data_generator_;
};

/**
 * @brief 简化版检查跨区域准备就绪条件节点
 */
class SimpleCheckCrossRegionReady : public SimpleBT::ConditionNode
{
public:
    SimpleCheckCrossRegionReady(const std::string& name, std::shared_ptr<CrossRegionContext> context)
        : SimpleBT::ConditionNode(name)
        , context_(context)
    {
    }

protected:
    SimpleBT::NodeStatus onTick() override
    {
        if (!context_)
            return SimpleBT::NodeStatus::FAILURE;

        if (!context_->is_initialized.load()) {
            if (context_->enable_debug_output) {
                std::cout << "[" << name() << "] Not initialized" << std::endl;
            }
            return SimpleBT::NodeStatus::FAILURE;
        }

        if (context_->is_completed.load()) {
            if (context_->enable_debug_output) {
                std::cout << "[" << name() << "] Already completed" << std::endl;
            }
            return SimpleBT::NodeStatus::FAILURE;
        }

        // 检查算法实例是否存在
        if (!context_->cross_region_alg) {
            if (context_->enable_debug_output) {
                std::cout << "[" << name() << "] Algorithm instance not available" << std::endl;
            }
            return SimpleBT::NodeStatus::FAILURE;
        }

        // 检查初始条件
        bool initial_conditions_ok = context_->cross_region_alg->CheckInitialConditions(context_->fusion_result);

        if (context_->enable_debug_output) {
            std::cout << "[" << name() << "] Ready: " << (initial_conditions_ok ? "YES" : "NO")
                      << ", State: " << asStringLiteral(context_->current_state) << std::endl;
        }

        return initial_conditions_ok ? SimpleBT::NodeStatus::SUCCESS : SimpleBT::NodeStatus::FAILURE;
    }

private:
    std::shared_ptr<CrossRegionContext> context_;
};

/**
 * @brief 简化版启用跨区域算法节点
 */
class SimpleEnableCrossRegion : public SimpleBT::ActionNode
{
public:
    SimpleEnableCrossRegion(const std::string& name, std::shared_ptr<CrossRegionContext> context)
        : SimpleBT::ActionNode(name)
        , context_(context)
    {
    }

protected:
    void onStart() override
    {
        if (!context_)
            return;

        if (context_->enable_debug_output) {
            std::cout << "[" << name() << "] Enabling cross region algorithm..." << std::endl;
        }

        context_->is_enabled.store(true);

        if (context_->cross_region_alg) {
            context_->cross_region_alg->SetVelPublisherProhibit(false);
        }
    }

    SimpleBT::NodeStatus onRunning() override
    {
        if (!context_)
            return SimpleBT::NodeStatus::FAILURE;
        return context_->is_enabled.load() ? SimpleBT::NodeStatus::SUCCESS : SimpleBT::NodeStatus::FAILURE;
    }

private:
    std::shared_ptr<CrossRegionContext> context_;
};

/**
 * @brief 简化版执行跨区域算法节点
 */
class SimpleExecuteCrossRegion : public SimpleBT::ActionNode
{
public:
    SimpleExecuteCrossRegion(const std::string& name, std::shared_ptr<CrossRegionContext> context)
        : SimpleBT::ActionNode(name)
        , context_(context)
        , data_generator_(std::make_unique<SimulationDataGenerator>())
        , execution_count_(0)
    {
    }

protected:
    void onStart() override
    {
        if (!context_)
            return;

        if (context_->enable_debug_output) {
            std::cout << "[" << name() << "] Starting cross region execution..." << std::endl;
        }

        execution_count_ = 0;
        last_execution_time_ = std::chrono::steady_clock::now();
    }

    SimpleBT::NodeStatus onRunning() override
    {
        if (!context_)
            return SimpleBT::NodeStatus::FAILURE;

        if (!context_->is_enabled.load() || !context_->cross_region_alg) {
            return SimpleBT::NodeStatus::FAILURE;
        }

        if (context_->is_completed.load()) {
            return SimpleBT::NodeStatus::SUCCESS;
        }

        // 更新仿真数据
        double simulation_time = context_->GetSimulationTime();
        data_generator_->UpdateSimulationData(*context_, simulation_time);
        context_->UpdateSimulationTime();

        // 执行跨区域算法
        try {
            context_->last_result = context_->cross_region_alg->DoCrossRegion(
                context_->fusion_result,
                context_->mark_loc_result,
                context_->imu_data,
                context_->mcu_exception_status);

            execution_count_++;

            // 检查是否完成
            if (context_->last_result.cross_region_completed) {
                context_->is_completed.store(true);
                context_->is_successful.store(context_->last_result.cross_region_status == CrossRegionStatus::Successed);

                // 设置最终结果
                context_->final_result.completed = true;
                context_->final_result.result = context_->is_successful.load();
                context_->final_result.timestamp = GetSteadyClockTimestampMs();

                if (context_->enable_debug_output) {
                    std::cout << "[" << name() << "] Cross region completed! Success: "
                              << context_->is_successful.load() << std::endl;
                }

                return SimpleBT::NodeStatus::SUCCESS;
            }

            // 定期输出状态信息
            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(now - last_execution_time_).count() > 1000) {
                if (context_->enable_debug_output) {
                    std::cout << "[" << name() << "] " << context_->GetStatusString()
                              << ", Executions: " << execution_count_ << std::endl;
                }
                last_execution_time_ = now;
            }

            return SimpleBT::NodeStatus::RUNNING;
        } catch (const std::exception& e) {
            std::cerr << "[" << name() << "] Execution failed: " << e.what() << std::endl;
            return SimpleBT::NodeStatus::FAILURE;
        }
    }

private:
    std::shared_ptr<CrossRegionContext> context_;
    std::unique_ptr<SimulationDataGenerator> data_generator_;
    int execution_count_;
    std::chrono::steady_clock::time_point last_execution_time_;
};

/**
 * @brief 简化版检查跨区域完成条件节点
 */
class SimpleCheckCrossRegionComplete : public SimpleBT::ConditionNode
{
public:
    SimpleCheckCrossRegionComplete(const std::string& name, std::shared_ptr<CrossRegionContext> context)
        : SimpleBT::ConditionNode(name)
        , context_(context)
    {
    }

protected:
    SimpleBT::NodeStatus onTick() override
    {
        if (!context_)
            return SimpleBT::NodeStatus::FAILURE;

        bool is_completed = context_->is_completed.load();

        if (context_->enable_debug_output && is_completed) {
            std::cout << "[" << name() << "] Cross region completed! Success: "
                      << context_->is_successful.load() << std::endl;
        }

        return is_completed ? SimpleBT::NodeStatus::SUCCESS : SimpleBT::NodeStatus::FAILURE;
    }

private:
    std::shared_ptr<CrossRegionContext> context_;
};

/**
 * @brief 简化版输出最终结果节点
 */
class SimpleOutputFinalResult : public SimpleBT::ActionNode
{
public:
    SimpleOutputFinalResult(const std::string& name, std::shared_ptr<CrossRegionContext> context)
        : SimpleBT::ActionNode(name)
        , context_(context)
    {
    }

protected:
    void onStart() override
    {
        if (!context_)
            return;

        std::cout << "\n========== Cross Region Simulation Final Result ==========" << std::endl;
        std::cout << "Simulation Time: " << context_->GetSimulationTime() << " seconds" << std::endl;
        std::cout << "Completed: " << (context_->final_result.completed ? "YES" : "NO") << std::endl;
        std::cout << "Success: " << (context_->final_result.result ? "YES" : "NO") << std::endl;
        std::cout << "Final State: " << asStringLiteral(context_->current_state) << std::endl;
        std::cout << "Mower State: " << asStringLiteral(context_->mower_state) << std::endl;

        if (context_->final_result.completed) {
            std::cout << "Cross Region Status: " << (context_->last_result.cross_region_status == CrossRegionStatus::Successed ? "SUCCESS" : context_->last_result.cross_region_status == CrossRegionStatus::Failed ? "FAILED"
                                                                                                                                                                                                                    : "IN_PROGRESS")
                      << std::endl;
        }

        std::cout << "Final Position: (" << context_->fusion_result.pose.x << ", "
                  << context_->fusion_result.pose.y << ", " << context_->fusion_result.pose.yaw << ")" << std::endl;
        std::cout << "========================================================\n"
                  << std::endl;
    }

    SimpleBT::NodeStatus onRunning() override
    {
        return SimpleBT::NodeStatus::SUCCESS;
    }

private:
    std::shared_ptr<CrossRegionContext> context_;
};

} // namespace fescue_iox
