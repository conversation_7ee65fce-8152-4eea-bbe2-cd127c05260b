#include <atomic>
#include <chrono>
#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <vector>

// 简化的行为树框架
namespace SimpleBT
{

enum class NodeStatus
{
    SUCCESS,
    FAILURE,
    RUNNING
};

const char* toStr(NodeStatus status)
{
    switch (status) {
    case NodeStatus::SUCCESS:
        return "SUCCESS";
    case NodeStatus::FAILURE:
        return "FAILURE";
    case NodeStatus::RUNNING:
        return "RUNNING";
    default:
        return "UNKNOWN";
    }
}

class TreeNode
{
public:
    TreeNode(const std::string& name)
        : name_(name)
    {
    }
    virtual ~TreeNode() = default;
    virtual NodeStatus tick() = 0;
    const std::string& name() const { return name_; }

protected:
    std::string name_;
};

class ActionNode : public TreeNode
{
public:
    ActionNode(const std::string& name)
        : TreeNode(name)
    {
    }

    NodeStatus tick() override
    {
        if (status_ != NodeStatus::RUNNING) {
            onStart();
        }
        status_ = onRunning();
        if (status_ != NodeStatus::RUNNING) {
            onHalted();
        }
        return status_;
    }

protected:
    virtual void onStart() {}
    virtual NodeStatus onRunning() = 0;
    virtual void onHalted() {}

private:
    NodeStatus status_ = NodeStatus::SUCCESS;
};

class SequenceNode : public TreeNode
{
public:
    SequenceNode(const std::string& name)
        : TreeNode(name)
    {
    }

    void addChild(std::shared_ptr<TreeNode> child)
    {
        children_.push_back(child);
    }

    NodeStatus tick() override
    {
        for (size_t i = current_child_; i < children_.size(); ++i) {
            NodeStatus child_status = children_[i]->tick();

            if (child_status == NodeStatus::RUNNING) {
                current_child_ = i;
                return NodeStatus::RUNNING;
            } else if (child_status == NodeStatus::FAILURE) {
                current_child_ = 0;
                return NodeStatus::FAILURE;
            }
        }
        current_child_ = 0;
        return NodeStatus::SUCCESS;
    }

private:
    std::vector<std::shared_ptr<TreeNode>> children_;
    size_t current_child_ = 0;
};

class BehaviorTree
{
public:
    BehaviorTree(std::shared_ptr<TreeNode> root)
        : root_(root)
    {
    }

    NodeStatus tick()
    {
        return root_->tick();
    }

    NodeStatus tickWhileRunning(int max_iterations = 100)
    {
        NodeStatus status = NodeStatus::RUNNING;
        int iterations = 0;

        while (status == NodeStatus::RUNNING && iterations < max_iterations) {
            status = tick();
            iterations++;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        return status;
    }

private:
    std::shared_ptr<TreeNode> root_;
};

} // namespace SimpleBT

// 简化的跨区域上下文
struct SimpleContext
{
    std::atomic<bool> is_initialized{false};
    std::atomic<bool> is_enabled{false};
    std::atomic<bool> is_completed{false};
    std::atomic<bool> is_successful{false};

    int current_step = 0;
    std::chrono::steady_clock::time_point start_time;

    void Reset()
    {
        is_initialized = false;
        is_enabled = false;
        is_completed = false;
        is_successful = false;
        current_step = 0;
        start_time = std::chrono::steady_clock::now();
    }

    double GetElapsedTime() const
    {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time);
        return duration.count() / 1000.0;
    }
};

// 简化的行为树节点实现
class InitCrossRegion : public SimpleBT::ActionNode
{
public:
    InitCrossRegion(const std::string& name, std::shared_ptr<SimpleContext> context)
        : ActionNode(name)
        , context_(context)
    {
    }

protected:
    void onStart() override
    {
        std::cout << "[" << context_->GetElapsedTime() << "s] 开始初始化跨区域算法..." << std::endl;
    }

    SimpleBT::NodeStatus onRunning() override
    {
        // 模拟初始化过程
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        context_->is_initialized = true;
        std::cout << "[" << context_->GetElapsedTime() << "s] 跨区域算法初始化完成" << std::endl;
        return SimpleBT::NodeStatus::SUCCESS;
    }

private:
    std::shared_ptr<SimpleContext> context_;
};

class CheckCrossRegionReady : public SimpleBT::ActionNode
{
public:
    CheckCrossRegionReady(const std::string& name, std::shared_ptr<SimpleContext> context)
        : ActionNode(name)
        , context_(context)
    {
    }

protected:
    void onStart() override
    {
        std::cout << "[" << context_->GetElapsedTime() << "s] 检查跨区域准备状态..." << std::endl;
    }

    SimpleBT::NodeStatus onRunning() override
    {
        if (!context_->is_initialized) {
            std::cout << "[" << context_->GetElapsedTime() << "s] 算法未初始化，检查失败" << std::endl;
            return SimpleBT::NodeStatus::FAILURE;
        }

        std::cout << "[" << context_->GetElapsedTime() << "s] 跨区域算法准备就绪" << std::endl;
        return SimpleBT::NodeStatus::SUCCESS;
    }

private:
    std::shared_ptr<SimpleContext> context_;
};

class EnableCrossRegion : public SimpleBT::ActionNode
{
public:
    EnableCrossRegion(const std::string& name, std::shared_ptr<SimpleContext> context)
        : ActionNode(name)
        , context_(context)
    {
    }

protected:
    void onStart() override
    {
        std::cout << "[" << context_->GetElapsedTime() << "s] 启用跨区域算法..." << std::endl;
    }

    SimpleBT::NodeStatus onRunning() override
    {
        context_->is_enabled = true;
        std::cout << "[" << context_->GetElapsedTime() << "s] 跨区域算法已启用" << std::endl;
        return SimpleBT::NodeStatus::SUCCESS;
    }

private:
    std::shared_ptr<SimpleContext> context_;
};

class ExecuteCrossRegion : public SimpleBT::ActionNode
{
public:
    ExecuteCrossRegion(const std::string& name, std::shared_ptr<SimpleContext> context)
        : ActionNode(name)
        , context_(context)
    {
    }

protected:
    void onStart() override
    {
        std::cout << "[" << context_->GetElapsedTime() << "s] 开始执行跨区域导航..." << std::endl;
        context_->current_step = 0;
    }

    SimpleBT::NodeStatus onRunning() override
    {
        if (!context_->is_enabled) {
            return SimpleBT::NodeStatus::FAILURE;
        }

        context_->current_step++;

        // 模拟不同的执行阶段
        switch (context_->current_step) {
        case 1:
            std::cout << "[" << context_->GetElapsedTime() << "s] 阶段1: 寻找信标..." << std::endl;
            break;
        case 2:
            std::cout << "[" << context_->GetElapsedTime() << "s] 阶段2: 检测到信标，开始定位..." << std::endl;
            break;
        case 3:
            std::cout << "[" << context_->GetElapsedTime() << "s] 阶段3: 定位完成，进入通道..." << std::endl;
            break;
        case 4:
            std::cout << "[" << context_->GetElapsedTime() << "s] 阶段4: 通道导航中..." << std::endl;
            break;
        case 5:
            std::cout << "[" << context_->GetElapsedTime() << "s] 阶段5: 到达目标区域" << std::endl;
            context_->is_completed = true;
            context_->is_successful = true;
            return SimpleBT::NodeStatus::SUCCESS;
        }

        // 模拟执行时间
        std::this_thread::sleep_for(std::chrono::milliseconds(800));

        return SimpleBT::NodeStatus::RUNNING;
    }

private:
    std::shared_ptr<SimpleContext> context_;
};

class OutputFinalResult : public SimpleBT::ActionNode
{
public:
    OutputFinalResult(const std::string& name, std::shared_ptr<SimpleContext> context)
        : ActionNode(name)
        , context_(context)
    {
    }

protected:
    void onStart() override
    {
        std::cout << "[" << context_->GetElapsedTime() << "s] 输出最终结果..." << std::endl;
    }

    SimpleBT::NodeStatus onRunning() override
    {
        std::cout << "\n========== 跨区域导航执行结果 ==========" << std::endl;
        std::cout << "初始化状态: " << (context_->is_initialized ? "成功" : "失败") << std::endl;
        std::cout << "启用状态: " << (context_->is_enabled ? "成功" : "失败") << std::endl;
        std::cout << "完成状态: " << (context_->is_completed ? "成功" : "失败") << std::endl;
        std::cout << "执行结果: " << (context_->is_successful ? "成功" : "失败") << std::endl;
        std::cout << "执行步骤: " << context_->current_step << std::endl;
        std::cout << "总耗时: " << context_->GetElapsedTime() << " 秒" << std::endl;
        std::cout << "=======================================" << std::endl;

        return SimpleBT::NodeStatus::SUCCESS;
    }

private:
    std::shared_ptr<SimpleContext> context_;
};

// 创建行为树
std::shared_ptr<SimpleBT::BehaviorTree> CreateCrossRegionTree(std::shared_ptr<SimpleContext> context)
{
    auto root = std::make_shared<SimpleBT::SequenceNode>("CrossRegionSequence");

    root->addChild(std::make_shared<InitCrossRegion>("InitCrossRegion", context));
    root->addChild(std::make_shared<CheckCrossRegionReady>("CheckReady", context));
    root->addChild(std::make_shared<EnableCrossRegion>("EnableCrossRegion", context));
    root->addChild(std::make_shared<ExecuteCrossRegion>("ExecuteCrossRegion", context));
    root->addChild(std::make_shared<OutputFinalResult>("OutputResult", context));

    return std::make_shared<SimpleBT::BehaviorTree>(root);
}

int main(int argc, char** argv)
{
    std::cout << "========== 独立跨区域行为树仿真测试 ==========" << std::endl;
    std::cout << "这是一个简化的行为树仿真，展示nav_cross_region的执行流程" << std::endl;
    std::cout << "=============================================" << std::endl;

    try {
        // 创建上下文
        auto context = std::make_shared<SimpleContext>();
        context->Reset();

        // 创建行为树
        auto tree = CreateCrossRegionTree(context);

        std::cout << "\n开始执行行为树..." << std::endl;

        // 执行行为树
        auto start_time = std::chrono::steady_clock::now();
        SimpleBT::NodeStatus status = tree->tickWhileRunning(50);
        auto end_time = std::chrono::steady_clock::now();

        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        std::cout << "\n行为树执行完成!" << std::endl;
        std::cout << "最终状态: " << SimpleBT::toStr(status) << std::endl;
        std::cout << "实际执行时间: " << duration.count() << " ms" << std::endl;

        return (status == SimpleBT::NodeStatus::SUCCESS) ? 0 : 1;

    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
}
