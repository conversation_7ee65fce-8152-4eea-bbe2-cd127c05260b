#include "cross_region_nodes.hpp"
#include "cross_region_context.hpp"
#include "simulation_data_generator.hpp"

#include "behaviortree_cpp/bt_factory.h"

#include <iostream>
#include <memory>
#include <string>
#include <chrono>
#include <thread>

using namespace BT;
using namespace fescue_iox;

/**
 * @brief 打印使用说明
 */
void PrintUsage(const char* program_name)
{
    std::cout << "Usage: " << program_name << " <xml_file> [tree_name] [options]" << std::endl;
    std::cout << std::endl;
    std::cout << "Arguments:" << std::endl;
    std::cout << "  xml_file    Path to the behavior tree XML file" << std::endl;
    std::cout << "  tree_name   Name of the behavior tree to execute (optional)" << std::endl;
    std::cout << "              Available trees: CrossRegionSimulation, SimpleCrossRegionTest," << std::endl;
    std::cout << "                               LoopCrossRegionTest, DebugCrossRegionTest" << std::endl;
    std::cout << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  --scenario <type>    Set simulation scenario (0=standard, 1=complex, 2=exception)" << std::endl;
    std::cout << "  --speed <factor>     Set simulation speed factor (default: 1.0)" << std::endl;
    std::cout << "  --quiet              Disable debug output" << std::endl;
    std::cout << "  --help               Show this help message" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << program_name << " cross_region_tree.xml" << std::endl;
    std::cout << "  " << program_name << " cross_region_tree.xml SimpleCrossRegionTest" << std::endl;
    std::cout << "  " << program_name << " cross_region_tree.xml DebugCrossRegionTest --scenario 1 --speed 2.0" << std::endl;
}

/**
 * @brief 解析命令行参数
 */
struct CommandLineArgs
{
    std::string xml_file;
    std::string tree_name = "CrossRegionSimulation";
    int scenario_type = 0;
    float simulation_speed = 1.0f;
    bool enable_debug = true;
    bool show_help = false;
};

CommandLineArgs ParseCommandLine(int argc, char** argv)
{
    CommandLineArgs args;
    
    if (argc < 2)
    {
        args.show_help = true;
        return args;
    }
    
    args.xml_file = argv[1];
    
    for (int i = 2; i < argc; ++i)
    {
        std::string arg = argv[i];
        
        if (arg == "--help" || arg == "-h")
        {
            args.show_help = true;
        }
        else if (arg == "--scenario" && i + 1 < argc)
        {
            args.scenario_type = std::atoi(argv[++i]);
        }
        else if (arg == "--speed" && i + 1 < argc)
        {
            args.simulation_speed = std::atof(argv[++i]);
        }
        else if (arg == "--quiet")
        {
            args.enable_debug = false;
        }
        else if (arg[0] != '-' && args.tree_name == "CrossRegionSimulation")
        {
            // 如果不是选项且还没设置tree_name，则认为是tree_name
            args.tree_name = arg;
        }
    }
    
    return args;
}

/**
 * @brief 注册所有行为树节点
 */
void RegisterNodes(BehaviorTreeFactory& factory)
{
    factory.registerNodeType<InitCrossRegion>("InitCrossRegion");
    factory.registerNodeType<CheckCrossRegionReady>("CheckCrossRegionReady");
    factory.registerNodeType<EnableCrossRegion>("EnableCrossRegion");
    factory.registerNodeType<ExecuteCrossRegion>("ExecuteCrossRegion");
    factory.registerNodeType<CheckCrossRegionComplete>("CheckCrossRegionComplete");
    factory.registerNodeType<DisableCrossRegion>("DisableCrossRegion");
    factory.registerNodeType<OutputFinalResult>("OutputFinalResult");
    factory.registerNodeType<ResetCrossRegion>("ResetCrossRegion");
}

/**
 * @brief 主函数
 */
int main(int argc, char** argv)
{
    // 解析命令行参数
    CommandLineArgs args = ParseCommandLine(argc, argv);
    
    if (args.show_help)
    {
        PrintUsage(argv[0]);
        return 0;
    }
    
    try
    {
        std::cout << "========== Cross Region Behavior Tree Simulation ==========" << std::endl;
        std::cout << "XML File: " << args.xml_file << std::endl;
        std::cout << "Tree Name: " << args.tree_name << std::endl;
        std::cout << "Scenario Type: " << args.scenario_type << std::endl;
        std::cout << "Simulation Speed: " << args.simulation_speed << "x" << std::endl;
        std::cout << "Debug Output: " << (args.enable_debug ? "Enabled" : "Disabled") << std::endl;
        std::cout << "============================================================" << std::endl;
        
        // 创建行为树工厂
        BehaviorTreeFactory factory;
        RegisterNodes(factory);
        
        // 创建跨区域上下文
        auto context = std::make_shared<CrossRegionContext>();
        context->simulation_speed = args.simulation_speed;
        context->enable_debug_output = args.enable_debug;
        
        // 创建仿真数据生成器并设置场景
        SimulationDataGenerator data_generator;
        data_generator.SetScenario(args.scenario_type);
        
        // 从XML文件创建行为树
        auto tree = factory.createTreeFromFile(args.xml_file, {{"context", context}});
        
        // 如果指定了特定的树名称，尝试创建该树
        if (args.tree_name != "CrossRegionSimulation")
        {
            try
            {
                tree = factory.createTree(args.tree_name, {{"context", context}});
            }
            catch (const std::exception& e)
            {
                std::cerr << "Failed to create tree '" << args.tree_name << "': " << e.what() << std::endl;
                std::cerr << "Falling back to default tree from file..." << std::endl;
                tree = factory.createTreeFromFile(args.xml_file, {{"context", context}});
            }
        }
        
        std::cout << "\nStarting behavior tree execution..." << std::endl;
        
        // 记录开始时间
        auto start_time = std::chrono::steady_clock::now();
        
        // 执行行为树
        BT::NodeStatus status = tree.tickWhileRunning(std::chrono::milliseconds(50));
        
        // 记录结束时间
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        std::cout << "\nBehavior tree execution completed!" << std::endl;
        std::cout << "Final Status: " << toStr(status) << std::endl;
        std::cout << "Real Execution Time: " << duration.count() << " ms" << std::endl;
        std::cout << "Simulation Time: " << context->GetSimulationTime() << " seconds" << std::endl;
        
        // 输出最终统计信息
        std::cout << "\n========== Execution Summary ==========" << std::endl;
        std::cout << "Initialized: " << (context->is_initialized.load() ? "YES" : "NO") << std::endl;
        std::cout << "Enabled: " << (context->is_enabled.load() ? "YES" : "NO") << std::endl;
        std::cout << "Completed: " << (context->is_completed.load() ? "YES" : "NO") << std::endl;
        std::cout << "Successful: " << (context->is_successful.load() ? "YES" : "NO") << std::endl;
        std::cout << "Final State: " << asStringLiteral(context->current_state) << std::endl;
        std::cout << "=======================================" << std::endl;
        
        return (status == BT::NodeStatus::SUCCESS) ? 0 : 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
