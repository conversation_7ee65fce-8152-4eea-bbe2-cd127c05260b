#pragma once

#include <memory>
#include <string>
#include <chrono>
#include <atomic>
#include <mutex>

#include "cross_region.hpp"
#include "cross_region_config.hpp"
#include "data_type.hpp"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_msgs/nav_cross_region_final_result__struct.h"
#include "ob_mower_msgs/nav_cross_region_state__struct.h"

namespace fescue_iox
{

/**
 * @brief 跨区域行为树仿真上下文
 * 包含算法实例、状态信息和模拟数据
 */
struct CrossRegionContext
{
    // 算法实例
    std::shared_ptr<NavigationCrossRegionAlg> cross_region_alg = nullptr;
    
    // 算法参数
    CrossRegionAlgParam alg_param;
    
    // 状态信息
    std::atomic<bool> is_initialized{false};
    std::atomic<bool> is_enabled{false};
    std::atomic<bool> is_completed{false};
    std::atomic<bool> is_successful{false};
    
    CrossRegionRunningState current_state{CrossRegionRunningState::UNDEFINED};
    MowerRunningState mower_state{MowerRunningState::IDLE};
    McuExceptionStatus mcu_exception_status{McuExceptionStatus::NORMAL};
    
    // 输入数据
    PerceptionFusionResult fusion_result;
    MarkLocationResult mark_loc_result;
    ImuData imu_data;
    
    // 输出数据
    CrossRegionAlgResult last_result;
    fescue_msgs__msg__NavCrossRegionFinalResult final_result;
    
    // 仿真控制
    std::atomic<bool> simulation_running{false};
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point last_update_time;
    
    // 仿真参数
    float simulation_speed{1.0f}; // 仿真速度倍数
    bool enable_debug_output{true};
    
    // 线程安全
    mutable std::mutex data_mutex;
    
    CrossRegionContext()
    {
        InitializeDefaultParams();
        InitializeDefaultData();
    }
    
    ~CrossRegionContext() = default;
    
    /**
     * @brief 初始化默认参数
     */
    void InitializeDefaultParams()
    {
        // 设置默认算法参数
        alg_param.cross_region_linear = 0.15f;
        alg_param.cross_region_angular = 0.5f;
        alg_param.max_distance_threshold = 0.95f;
        alg_param.min_distance_threshold = 0.65f;
        alg_param.cross_region_special_linear = 0.15f;
        alg_param.cross_region_special_angular = 0.2f;
        alg_param.dis_tolerance = 0.0f;
        alg_param.cross_region_angle_compensation = 0.0f;
        alg_param.channel_stop_pose_x = -0.5f;
        alg_param.grass_count_threshold = 5;
        alg_param.edge_mode_direction = -1;
        alg_param.channel_width = 0.8f;
        alg_param.camera_2_center_dis = 0.37f;
        alg_param.adjust_mode_x_direction_threshold = -0.5f;
        alg_param.mark_distance_threshold = 0.5f;
        alg_param.perception_drive_cooldown_time_threshold = 3;
        alg_param.cross_region_adjust_displace = 0.7f;
        alg_param.channel_fixed_distance = 0.2f;
    }
    
    /**
     * @brief 初始化默认数据
     */
    void InitializeDefaultData()
    {
        // 初始化感知融合结果
        fusion_result.Reset();
        fusion_result.pose.x = 0.0f;
        fusion_result.pose.y = 0.0f;
        fusion_result.pose.yaw = 0.0f;
        fusion_result.timestamp = GetSteadyClockTimestampMs();
        
        // 初始化标记定位结果
        mark_loc_result.Reset();
        
        // 初始化IMU数据
        imu_data.Reset();
        imu_data.timestamp = GetSteadyClockTimestampMs();
        
        // 初始化最终结果
        final_result.timestamp = 0;
        final_result.completed = false;
        final_result.result = false;
        
        start_time = std::chrono::steady_clock::now();
        last_update_time = start_time;
    }
    
    /**
     * @brief 更新仿真时间
     */
    void UpdateSimulationTime()
    {
        last_update_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            last_update_time - start_time).count();
        
        // 更新数据时间戳
        uint64_t current_timestamp = GetSteadyClockTimestampMs();
        fusion_result.timestamp = current_timestamp;
        mark_loc_result.timestamp = current_timestamp;
        imu_data.timestamp = current_timestamp;
    }
    
    /**
     * @brief 获取仿真运行时间（秒）
     */
    double GetSimulationTime() const
    {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - start_time).count();
        return elapsed / 1000.0 * simulation_speed;
    }
    
    /**
     * @brief 重置上下文状态
     */
    void Reset()
    {
        std::lock_guard<std::mutex> lock(data_mutex);
        
        is_initialized.store(false);
        is_enabled.store(false);
        is_completed.store(false);
        is_successful.store(false);
        
        current_state = CrossRegionRunningState::UNDEFINED;
        mower_state = MowerRunningState::IDLE;
        mcu_exception_status = McuExceptionStatus::NORMAL;
        
        InitializeDefaultData();
        
        if (cross_region_alg)
        {
            cross_region_alg->ResetCrossRegionFlags();
        }
    }
    
    /**
     * @brief 获取状态字符串
     */
    std::string GetStatusString() const
    {
        std::string status = "CrossRegion Status: ";
        status += "Init=" + std::to_string(is_initialized.load()) + ", ";
        status += "Enabled=" + std::to_string(is_enabled.load()) + ", ";
        status += "Completed=" + std::to_string(is_completed.load()) + ", ";
        status += "Successful=" + std::to_string(is_successful.load()) + ", ";
        status += "State=" + asStringLiteral(current_state) + ", ";
        status += "MowerState=" + asStringLiteral(mower_state) + ", ";
        status += "SimTime=" + std::to_string(GetSimulationTime()) + "s";
        return status;
    }
};

} // namespace fescue_iox
