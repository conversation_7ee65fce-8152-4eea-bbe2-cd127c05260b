#pragma once

#include <memory>
#include <string>
#include <functional>
#include <iostream>
#include <chrono>
#include <thread>

namespace SimpleBT
{

/**
 * @brief 节点状态枚举
 */
enum class NodeStatus
{
    SUCCESS,
    FAILURE,
    RUNNING
};

/**
 * @brief 将节点状态转换为字符串
 */
inline std::string toStr(NodeStatus status)
{
    switch (status)
    {
    case NodeStatus::SUCCESS: return "SUCCESS";
    case NodeStatus::FAILURE: return "FAILURE";
    case NodeStatus::RUNNING: return "RUNNING";
    default: return "UNKNOWN";
    }
}

/**
 * @brief 行为树节点基类
 */
class TreeNode
{
public:
    TreeNode(const std::string& name) : name_(name) {}
    virtual ~TreeNode() = default;
    
    virtual NodeStatus tick() = 0;
    
    const std::string& name() const { return name_; }

protected:
    std::string name_;
};

/**
 * @brief 动作节点基类
 */
class ActionNode : public TreeNode
{
public:
    ActionNode(const std::string& name) : TreeNode(name), is_started_(false) {}
    
    NodeStatus tick() override
    {
        if (!is_started_)
        {
            onStart();
            is_started_ = true;
            return NodeStatus::RUNNING;
        }
        
        NodeStatus status = onRunning();
        if (status != NodeStatus::RUNNING)
        {
            onHalted();
            is_started_ = false;
        }
        return status;
    }
    
protected:
    virtual void onStart() = 0;
    virtual NodeStatus onRunning() = 0;
    virtual void onHalted() {}
    
private:
    bool is_started_;
};

/**
 * @brief 条件节点基类
 */
class ConditionNode : public TreeNode
{
public:
    ConditionNode(const std::string& name) : TreeNode(name) {}
    
    NodeStatus tick() override
    {
        return onTick();
    }
    
protected:
    virtual NodeStatus onTick() = 0;
};

/**
 * @brief 序列节点 - 按顺序执行所有子节点
 */
class SequenceNode : public TreeNode
{
public:
    SequenceNode(const std::string& name) : TreeNode(name), current_child_(0) {}
    
    void addChild(std::shared_ptr<TreeNode> child)
    {
        children_.push_back(child);
    }
    
    NodeStatus tick() override
    {
        while (current_child_ < children_.size())
        {
            NodeStatus status = children_[current_child_]->tick();
            
            if (status == NodeStatus::RUNNING)
            {
                return NodeStatus::RUNNING;
            }
            else if (status == NodeStatus::FAILURE)
            {
                current_child_ = 0; // 重置
                return NodeStatus::FAILURE;
            }
            else // SUCCESS
            {
                current_child_++;
            }
        }
        
        current_child_ = 0; // 重置
        return NodeStatus::SUCCESS;
    }
    
private:
    std::vector<std::shared_ptr<TreeNode>> children_;
    size_t current_child_;
};

/**
 * @brief 选择节点 - 执行第一个成功的子节点
 */
class FallbackNode : public TreeNode
{
public:
    FallbackNode(const std::string& name) : TreeNode(name), current_child_(0) {}
    
    void addChild(std::shared_ptr<TreeNode> child)
    {
        children_.push_back(child);
    }
    
    NodeStatus tick() override
    {
        while (current_child_ < children_.size())
        {
            NodeStatus status = children_[current_child_]->tick();
            
            if (status == NodeStatus::RUNNING)
            {
                return NodeStatus::RUNNING;
            }
            else if (status == NodeStatus::SUCCESS)
            {
                current_child_ = 0; // 重置
                return NodeStatus::SUCCESS;
            }
            else // FAILURE
            {
                current_child_++;
            }
        }
        
        current_child_ = 0; // 重置
        return NodeStatus::FAILURE;
    }
    
private:
    std::vector<std::shared_ptr<TreeNode>> children_;
    size_t current_child_;
};

/**
 * @brief 反转节点 - 反转子节点的结果
 */
class InverterNode : public TreeNode
{
public:
    InverterNode(const std::string& name) : TreeNode(name) {}
    
    void setChild(std::shared_ptr<TreeNode> child)
    {
        child_ = child;
    }
    
    NodeStatus tick() override
    {
        if (!child_) return NodeStatus::FAILURE;
        
        NodeStatus status = child_->tick();
        
        if (status == NodeStatus::SUCCESS)
            return NodeStatus::FAILURE;
        else if (status == NodeStatus::FAILURE)
            return NodeStatus::SUCCESS;
        else
            return NodeStatus::RUNNING;
    }
    
private:
    std::shared_ptr<TreeNode> child_;
};

/**
 * @brief 重复节点 - 重复执行子节点指定次数
 */
class RepeatNode : public TreeNode
{
public:
    RepeatNode(const std::string& name, int num_cycles) 
        : TreeNode(name), num_cycles_(num_cycles), current_cycle_(0) {}
    
    void setChild(std::shared_ptr<TreeNode> child)
    {
        child_ = child;
    }
    
    NodeStatus tick() override
    {
        if (!child_) return NodeStatus::FAILURE;
        
        while (current_cycle_ < num_cycles_)
        {
            NodeStatus status = child_->tick();
            
            if (status == NodeStatus::RUNNING)
            {
                return NodeStatus::RUNNING;
            }
            else if (status == NodeStatus::FAILURE)
            {
                current_cycle_ = 0; // 重置
                return NodeStatus::FAILURE;
            }
            else // SUCCESS
            {
                current_cycle_++;
            }
        }
        
        current_cycle_ = 0; // 重置
        return NodeStatus::SUCCESS;
    }
    
private:
    std::shared_ptr<TreeNode> child_;
    int num_cycles_;
    int current_cycle_;
};

/**
 * @brief 简单的行为树执行器
 */
class BehaviorTree
{
public:
    BehaviorTree(std::shared_ptr<TreeNode> root) : root_(root) {}
    
    NodeStatus tick()
    {
        if (!root_) return NodeStatus::FAILURE;
        return root_->tick();
    }
    
    /**
     * @brief 持续执行直到完成
     */
    NodeStatus tickWhileRunning(int tick_interval_ms = 50)
    {
        NodeStatus status = NodeStatus::RUNNING;
        
        while (status == NodeStatus::RUNNING)
        {
            status = tick();
            
            if (status == NodeStatus::RUNNING)
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(tick_interval_ms));
            }
        }
        
        return status;
    }
    
private:
    std::shared_ptr<TreeNode> root_;
};

} // namespace SimpleBT
