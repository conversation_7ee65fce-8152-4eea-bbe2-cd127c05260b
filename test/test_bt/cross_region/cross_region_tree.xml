<?xml version="1.0" encoding="UTF-8"?>
<root BTCPP_format="4">
  <!-- 跨区域行为树仿真 -->
  <BehaviorTree ID="CrossRegionSimulation">
    <Sequence name="MainSequence">
      
      <!-- 初始化阶段 -->
      <Sequence name="InitializationPhase">
        <InitCrossRegion name="InitCrossRegion" context_ptr="{context}"/>
        <CheckCrossRegionReady name="CheckReady" context_ptr="{context}"/>
      </Sequence>
      
      <!-- 执行阶段 -->
      <Sequence name="ExecutionPhase">
        <EnableCrossRegion name="EnableCrossRegion" context_ptr="{context}"/>
        
        <!-- 主执行循环 -->
        <ReactiveSequence name="MainExecutionLoop">
          <Inverter name="NotCompleted">
            <CheckCrossRegionComplete name="CheckComplete" context_ptr="{context}"/>
          </Inverter>
          <ExecuteCrossRegion name="ExecuteCrossRegion" context_ptr="{context}"/>
        </ReactiveSequence>
        
      </Sequence>
      
      <!-- 完成阶段 -->
      <Sequence name="CompletionPhase">
        <DisableCrossRegion name="DisableCrossRegion" context_ptr="{context}"/>
        <OutputFinalResult name="OutputResult" context_ptr="{context}"/>
      </Sequence>
      
    </Sequence>
  </BehaviorTree>
  
  <!-- 简化版跨区域行为树（用于快速测试） -->
  <BehaviorTree ID="SimpleCrossRegionTest">
    <Sequence name="SimpleSequence">
      <InitCrossRegion name="Init" context_ptr="{context}"/>
      <EnableCrossRegion name="Enable" context_ptr="{context}"/>
      <ExecuteCrossRegion name="Execute" context_ptr="{context}"/>
      <OutputFinalResult name="Output" context_ptr="{context}"/>
    </Sequence>
  </BehaviorTree>
  
  <!-- 循环测试版本（用于多次测试） -->
  <BehaviorTree ID="LoopCrossRegionTest">
    <Repeat num_cycles="3" name="TestLoop">
      <Sequence name="SingleTestRun">
        
        <!-- 重置状态 -->
        <ResetCrossRegion name="Reset" context_ptr="{context}"/>
        
        <!-- 执行测试 -->
        <Fallback name="TestExecution">
          <Sequence name="NormalExecution">
            <InitCrossRegion name="Init" context_ptr="{context}"/>
            <CheckCrossRegionReady name="CheckReady" context_ptr="{context}"/>
            <EnableCrossRegion name="Enable" context_ptr="{context}"/>
            
            <!-- 带超时的执行循环 -->
            <Timeout msec="30000" name="ExecutionTimeout">
              <ReactiveSequence name="ExecutionLoop">
                <Inverter name="NotCompleted">
                  <CheckCrossRegionComplete name="CheckComplete" context_ptr="{context}"/>
                </Inverter>
                <ExecuteCrossRegion name="Execute" context_ptr="{context}"/>
              </ReactiveSequence>
            </Timeout>
            
            <DisableCrossRegion name="Disable" context_ptr="{context}"/>
            <OutputFinalResult name="Output" context_ptr="{context}"/>
          </Sequence>
          
          <!-- 失败处理 -->
          <Sequence name="FailureHandling">
            <DisableCrossRegion name="DisableOnFailure" context_ptr="{context}"/>
            <OutputFinalResult name="OutputOnFailure" context_ptr="{context}"/>
          </Sequence>
        </Fallback>
        
      </Sequence>
    </Repeat>
  </BehaviorTree>
  
  <!-- 调试版本（详细输出） -->
  <BehaviorTree ID="DebugCrossRegionTest">
    <Sequence name="DebugSequence">
      
      <!-- 初始化并检查 -->
      <Sequence name="InitAndCheck">
        <InitCrossRegion name="DebugInit" context_ptr="{context}"/>
        <Delay delay_msec="1000" name="InitDelay"/>
        <CheckCrossRegionReady name="DebugCheckReady" context_ptr="{context}"/>
      </Sequence>
      
      <!-- 启用并执行 -->
      <Sequence name="EnableAndExecute">
        <EnableCrossRegion name="DebugEnable" context_ptr="{context}"/>
        <Delay delay_msec="500" name="EnableDelay"/>
        
        <!-- 分步执行，便于调试 -->
        <ReactiveSequence name="StepByStepExecution">
          <Inverter name="NotCompleted">
            <CheckCrossRegionComplete name="DebugCheckComplete" context_ptr="{context}"/>
          </Inverter>
          <Sequence name="ExecuteWithDelay">
            <ExecuteCrossRegion name="DebugExecute" context_ptr="{context}"/>
            <Delay delay_msec="100" name="ExecutionDelay"/>
          </Sequence>
        </ReactiveSequence>
        
      </Sequence>
      
      <!-- 清理和输出 -->
      <Sequence name="CleanupAndOutput">
        <Delay delay_msec="1000" name="CompletionDelay"/>
        <DisableCrossRegion name="DebugDisable" context_ptr="{context}"/>
        <OutputFinalResult name="DebugOutput" context_ptr="{context}"/>
      </Sequence>
      
    </Sequence>
  </BehaviorTree>
  
</root>
