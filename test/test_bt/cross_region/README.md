# Nav Cross Region 行为树仿真测试

这是一个基于行为树的nav_cross_region节点仿真测试系统，可以在不修改原始nav_cross_region节点代码的情况下，通过行为树的方式运行和测试跨区域功能。

## 功能特性

- **完整的行为树框架**: 基于BehaviorTree.CPP实现
- **模块化设计**: 每个功能都封装为独立的行为树节点
- **仿真数据生成**: 自动生成模拟的感知融合数据、标记定位数据、IMU数据等
- **多种测试场景**: 支持标准、复杂、异常等多种测试场景
- **可扩展性**: 保持原始nav_cross_region节点不变，通过行为树扩展功能

## 文件结构

```
cross_region/
├── README.md                      # 说明文档
├── cross_region_context.hpp       # 跨区域上下文结构定义
├── simulation_data_generator.hpp  # 仿真数据生成器
├── cross_region_nodes.hpp         # 行为树节点类定义
├── cross_region_tree.xml          # 行为树XML配置文件
└── test_cross_region_bt.cpp       # 测试主程序
```

## 行为树节点说明

### Action节点
- **InitCrossRegion**: 初始化跨区域算法实例和参数
- **EnableCrossRegion**: 启用跨区域算法
- **ExecuteCrossRegion**: 执行跨区域算法主循环
- **DisableCrossRegion**: 禁用跨区域算法
- **OutputFinalResult**: 输出最终测试结果
- **ResetCrossRegion**: 重置跨区域上下文状态

### Condition节点
- **CheckCrossRegionReady**: 检查跨区域算法是否准备就绪
- **CheckCrossRegionComplete**: 检查跨区域是否完成

## 可用的行为树

1. **CrossRegionSimulation**: 完整的跨区域仿真流程
2. **SimpleCrossRegionTest**: 简化版测试（用于快速验证）
3. **LoopCrossRegionTest**: 循环测试版本（多次重复测试）
4. **DebugCrossRegionTest**: 调试版本（详细输出和延时）

## 编译方法

在项目根目录下执行：

```bash
mkdir -p build
cd build
cmake ..
make test_cross_region_bt
```

## 使用方法

### 基本用法

```bash
# 使用默认配置运行
./test_cross_region_bt cross_region_tree.xml

# 指定特定的行为树
./test_cross_region_bt cross_region_tree.xml SimpleCrossRegionTest

# 使用调试模式
./test_cross_region_bt cross_region_tree.xml DebugCrossRegionTest
```

### 高级选项

```bash
# 设置仿真场景类型
./test_cross_region_bt cross_region_tree.xml --scenario 1

# 设置仿真速度倍数
./test_cross_region_bt cross_region_tree.xml --speed 2.0

# 禁用调试输出
./test_cross_region_bt cross_region_tree.xml --quiet

# 组合使用
./test_cross_region_bt cross_region_tree.xml LoopCrossRegionTest --scenario 2 --speed 0.5
```

## 仿真场景

- **场景0 (标准)**: 标准跨区域场景，两个信标，直线路径
- **场景1 (复杂)**: 复杂场景，多个信标，斜向路径
- **场景2 (异常)**: 异常场景，单个信标，测试异常处理

## 输出说明

程序会输出详细的执行信息，包括：
- 初始化状态
- 算法执行过程
- 状态变化
- 最终结果统计

## 扩展说明

### 添加新的行为树节点

1. 在`cross_region_nodes.hpp`中定义新的节点类
2. 在`test_cross_region_bt.cpp`的`RegisterNodes`函数中注册新节点
3. 在XML文件中使用新节点

### 添加新的仿真场景

1. 在`simulation_data_generator.hpp`中的`InitializeScenario`函数中添加新场景
2. 设置相应的轨迹参数和信标位置

### 自定义行为树

1. 在XML文件中定义新的BehaviorTree
2. 使用现有节点组合出新的执行逻辑
3. 通过命令行参数指定新的行为树名称

## 注意事项

1. 确保所有依赖的头文件和库都已正确配置
2. 仿真数据是模拟生成的，可能与实际环境有差异
3. 调试模式会产生大量输出，建议在需要时才启用
4. 循环测试模式适合进行稳定性和性能测试

## 故障排除

如果遇到编译错误，请检查：
1. CMakeLists.txt中的路径是否正确
2. 所有依赖的源文件是否存在
3. BehaviorTree.CPP库是否正确安装

如果遇到运行时错误，请检查：
1. XML文件路径是否正确
2. 行为树名称是否存在
3. 仿真参数是否合理
