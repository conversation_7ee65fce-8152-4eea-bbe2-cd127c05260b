#pragma once

#include <random>
#include <cmath>
#include <vector>

#include "cross_region_context.hpp"
#include "data_type.hpp"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"

namespace fescue_iox
{

/**
 * @brief 跨区域仿真数据生成器
 * 生成模拟的感知融合数据、标记定位数据、IMU数据等
 */
class SimulationDataGenerator
{
public:
    SimulationDataGenerator()
        : random_engine_(std::random_device{}())
        , noise_distribution_(0.0, 1.0)
    {
        InitializeScenario();
    }
    
    ~SimulationDataGenerator() = default;
    
    /**
     * @brief 更新仿真数据
     * @param context 跨区域上下文
     * @param simulation_time 仿真时间（秒）
     */
    void UpdateSimulationData(CrossRegionContext& context, double simulation_time)
    {
        UpdatePerceptionFusionResult(context.fusion_result, simulation_time);
        UpdateMarkLocationResult(context.mark_loc_result, simulation_time);
        UpdateImuData(context.imu_data, simulation_time);
        
        // 更新时间戳
        uint64_t current_timestamp = GetSteadyClockTimestampMs();
        context.fusion_result.timestamp = current_timestamp;
        context.mark_loc_result.timestamp = current_timestamp;
        context.imu_data.timestamp = current_timestamp;
    }
    
    /**
     * @brief 设置仿真场景
     * @param scenario_type 场景类型
     */
    void SetScenario(int scenario_type)
    {
        scenario_type_ = scenario_type;
        InitializeScenario();
    }
    
private:
    std::mt19937 random_engine_;
    std::normal_distribution<double> noise_distribution_;
    
    int scenario_type_{0}; // 0: 标准跨区域场景, 1: 复杂场景, 2: 异常场景
    
    // 仿真轨迹参数
    struct TrajectoryParams
    {
        float start_x{0.0f};
        float start_y{0.0f};
        float start_yaw{0.0f};
        float target_x{2.0f};
        float target_y{0.0f};
        float target_yaw{0.0f};
        float velocity{0.15f};
        std::vector<std::pair<float, float>> beacon_positions;
    } trajectory_params_;
    
    /**
     * @brief 初始化仿真场景
     */
    void InitializeScenario()
    {
        switch (scenario_type_)
        {
        case 0: // 标准跨区域场景
            trajectory_params_.start_x = 0.0f;
            trajectory_params_.start_y = 0.0f;
            trajectory_params_.start_yaw = 0.0f;
            trajectory_params_.target_x = 2.0f;
            trajectory_params_.target_y = 0.0f;
            trajectory_params_.target_yaw = 0.0f;
            trajectory_params_.velocity = 0.15f;
            trajectory_params_.beacon_positions = {{1.0f, -0.5f}, {1.0f, 0.5f}};
            break;
            
        case 1: // 复杂场景
            trajectory_params_.start_x = 0.0f;
            trajectory_params_.start_y = 0.0f;
            trajectory_params_.start_yaw = 0.0f;
            trajectory_params_.target_x = 3.0f;
            trajectory_params_.target_y = 1.0f;
            trajectory_params_.target_yaw = M_PI / 4;
            trajectory_params_.velocity = 0.12f;
            trajectory_params_.beacon_positions = {{1.5f, -0.3f}, {1.5f, 0.3f}, {2.5f, 0.7f}};
            break;
            
        case 2: // 异常场景
            trajectory_params_.start_x = 0.0f;
            trajectory_params_.start_y = 0.0f;
            trajectory_params_.start_yaw = 0.0f;
            trajectory_params_.target_x = 1.5f;
            trajectory_params_.target_y = 0.0f;
            trajectory_params_.target_yaw = 0.0f;
            trajectory_params_.velocity = 0.10f;
            trajectory_params_.beacon_positions = {{0.8f, -0.4f}}; // 只有一个信标
            break;
            
        default:
            scenario_type_ = 0;
            InitializeScenario();
            break;
        }
    }
    
    /**
     * @brief 更新感知融合结果
     */
    void UpdatePerceptionFusionResult(PerceptionFusionResult& fusion_result, double simulation_time)
    {
        // 模拟机器人沿轨迹移动
        float progress = std::min(1.0f, static_cast<float>(simulation_time / 10.0)); // 10秒完成跨区域
        
        // 插值计算当前位置
        fusion_result.pose.x = trajectory_params_.start_x + 
            (trajectory_params_.target_x - trajectory_params_.start_x) * progress;
        fusion_result.pose.y = trajectory_params_.start_y + 
            (trajectory_params_.target_y - trajectory_params_.start_y) * progress;
        fusion_result.pose.yaw = trajectory_params_.start_yaw + 
            (trajectory_params_.target_yaw - trajectory_params_.start_yaw) * progress;
        
        // 添加噪声
        fusion_result.pose.x += static_cast<float>(noise_distribution_(random_engine_) * 0.02);
        fusion_result.pose.y += static_cast<float>(noise_distribution_(random_engine_) * 0.02);
        fusion_result.pose.yaw += static_cast<float>(noise_distribution_(random_engine_) * 0.05);
        
        // 模拟速度
        fusion_result.velocity.linear_x = trajectory_params_.velocity * (1.0f - progress * 0.2f);
        fusion_result.velocity.linear_y = 0.0f;
        fusion_result.velocity.angular_z = 0.0f;
        
        // 模拟草地检测
        if (simulation_time < 2.0)
        {
            fusion_result.grass_count = 8; // 在草地上
        }
        else if (simulation_time < 8.0)
        {
            fusion_result.grass_count = 2; // 在通道中
        }
        else
        {
            fusion_result.grass_count = 7; // 到达目标草地
        }
        
        fusion_result.is_valid = true;
    }
    
    /**
     * @brief 更新标记定位结果
     */
    void UpdateMarkLocationResult(MarkLocationResult& mark_loc_result, double simulation_time)
    {
        mark_loc_result.Reset();
        
        // 根据仿真时间和位置决定是否检测到信标
        if (simulation_time > 1.0 && simulation_time < 9.0)
        {
            // 在跨区域过程中可能检测到信标
            for (size_t i = 0; i < trajectory_params_.beacon_positions.size() && i < 2; ++i)
            {
                auto& beacon_pos = trajectory_params_.beacon_positions[i];
                
                // 计算与信标的距离
                float current_x = trajectory_params_.start_x + 
                    (trajectory_params_.target_x - trajectory_params_.start_x) * 
                    std::min(1.0f, static_cast<float>(simulation_time / 10.0));
                float current_y = trajectory_params_.start_y + 
                    (trajectory_params_.target_y - trajectory_params_.start_y) * 
                    std::min(1.0f, static_cast<float>(simulation_time / 10.0));
                
                float distance = std::sqrt(std::pow(beacon_pos.first - current_x, 2) + 
                                         std::pow(beacon_pos.second - current_y, 2));
                
                // 如果在检测范围内
                if (distance < 1.5f)
                {
                    if (i == 0)
                    {
                        mark_loc_result.mark_id_1 = 1;
                        mark_loc_result.mark_distance_1 = distance + 
                            static_cast<float>(noise_distribution_(random_engine_) * 0.05);
                        mark_loc_result.mark_angle_1 = std::atan2(beacon_pos.second - current_y, 
                                                                beacon_pos.first - current_x) + 
                            static_cast<float>(noise_distribution_(random_engine_) * 0.1);
                    }
                    else if (i == 1)
                    {
                        mark_loc_result.mark_id_2 = 2;
                        mark_loc_result.mark_distance_2 = distance + 
                            static_cast<float>(noise_distribution_(random_engine_) * 0.05);
                        mark_loc_result.mark_angle_2 = std::atan2(beacon_pos.second - current_y, 
                                                                beacon_pos.first - current_x) + 
                            static_cast<float>(noise_distribution_(random_engine_) * 0.1);
                    }
                }
            }
        }
        
        mark_loc_result.is_valid = (mark_loc_result.mark_id_1 > 0 || mark_loc_result.mark_id_2 > 0);
    }
    
    /**
     * @brief 更新IMU数据
     */
    void UpdateImuData(ImuData& imu_data, double simulation_time)
    {
        // 模拟IMU数据
        imu_data.accel_x = static_cast<float>(noise_distribution_(random_engine_) * 0.1);
        imu_data.accel_y = static_cast<float>(noise_distribution_(random_engine_) * 0.1);
        imu_data.accel_z = 9.8f + static_cast<float>(noise_distribution_(random_engine_) * 0.2);
        
        imu_data.gyro_x = static_cast<float>(noise_distribution_(random_engine_) * 0.01);
        imu_data.gyro_y = static_cast<float>(noise_distribution_(random_engine_) * 0.01);
        imu_data.gyro_z = static_cast<float>(noise_distribution_(random_engine_) * 0.02);
        
        // 模拟偏航角变化
        float target_yaw = trajectory_params_.start_yaw + 
            (trajectory_params_.target_yaw - trajectory_params_.start_yaw) * 
            std::min(1.0f, static_cast<float>(simulation_time / 10.0));
        imu_data.yaw = target_yaw + static_cast<float>(noise_distribution_(random_engine_) * 0.05);
        
        imu_data.is_valid = true;
    }
};

} // namespace fescue_iox
