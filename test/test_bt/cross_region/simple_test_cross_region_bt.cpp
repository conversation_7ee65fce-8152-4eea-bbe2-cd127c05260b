#include "simple_bt_framework.hpp"
#include "simple_cross_region_nodes.hpp"
#include "cross_region_context.hpp"
#include "simulation_data_generator.hpp"

#include <iostream>
#include <memory>
#include <string>
#include <chrono>

using namespace SimpleBT;
using namespace fescue_iox;

/**
 * @brief 打印使用说明
 */
void PrintUsage(const char* program_name)
{
    std::cout << "Usage: " << program_name << " [options]" << std::endl;
    std::cout << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  --scenario <type>    Set simulation scenario (0=standard, 1=complex, 2=exception)" << std::endl;
    std::cout << "  --speed <factor>     Set simulation speed factor (default: 1.0)" << std::endl;
    std::cout << "  --quiet              Disable debug output" << std::endl;
    std::cout << "  --help               Show this help message" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << program_name << std::endl;
    std::cout << "  " << program_name << " --scenario 1 --speed 2.0" << std::endl;
    std::cout << "  " << program_name << " --quiet" << std::endl;
}

/**
 * @brief 解析命令行参数
 */
struct CommandLineArgs
{
    int scenario_type = 0;
    float simulation_speed = 1.0f;
    bool enable_debug = true;
    bool show_help = false;
};

CommandLineArgs ParseCommandLine(int argc, char** argv)
{
    CommandLineArgs args;
    
    for (int i = 1; i < argc; ++i)
    {
        std::string arg = argv[i];
        
        if (arg == "--help" || arg == "-h")
        {
            args.show_help = true;
        }
        else if (arg == "--scenario" && i + 1 < argc)
        {
            args.scenario_type = std::atoi(argv[++i]);
        }
        else if (arg == "--speed" && i + 1 < argc)
        {
            args.simulation_speed = std::atof(argv[++i]);
        }
        else if (arg == "--quiet")
        {
            args.enable_debug = false;
        }
    }
    
    return args;
}

/**
 * @brief 创建简单的跨区域行为树
 */
std::shared_ptr<BehaviorTree> CreateSimpleCrossRegionTree(std::shared_ptr<CrossRegionContext> context)
{
    // 创建序列节点作为根节点
    auto root = std::make_shared<SequenceNode>("CrossRegionSequence");
    
    // 添加子节点
    root->addChild(std::make_shared<SimpleInitCrossRegion>("InitCrossRegion", context));
    root->addChild(std::make_shared<SimpleCheckCrossRegionReady>("CheckReady", context));
    root->addChild(std::make_shared<SimpleEnableCrossRegion>("EnableCrossRegion", context));
    root->addChild(std::make_shared<SimpleExecuteCrossRegion>("ExecuteCrossRegion", context));
    root->addChild(std::make_shared<SimpleOutputFinalResult>("OutputResult", context));
    
    return std::make_shared<BehaviorTree>(root);
}

/**
 * @brief 创建带循环的跨区域行为树
 */
std::shared_ptr<BehaviorTree> CreateLoopCrossRegionTree(std::shared_ptr<CrossRegionContext> context)
{
    // 创建主序列
    auto main_sequence = std::make_shared<SequenceNode>("MainSequence");
    
    // 初始化
    main_sequence->addChild(std::make_shared<SimpleInitCrossRegion>("InitCrossRegion", context));
    main_sequence->addChild(std::make_shared<SimpleCheckCrossRegionReady>("CheckReady", context));
    main_sequence->addChild(std::make_shared<SimpleEnableCrossRegion>("EnableCrossRegion", context));
    
    // 创建执行循环序列
    auto execution_sequence = std::make_shared<SequenceNode>("ExecutionLoop");
    
    // 创建反转节点检查是否未完成
    auto not_completed = std::make_shared<InverterNode>("NotCompleted");
    not_completed->setChild(std::make_shared<SimpleCheckCrossRegionComplete>("CheckComplete", context));
    execution_sequence->addChild(not_completed);
    
    // 执行节点
    execution_sequence->addChild(std::make_shared<SimpleExecuteCrossRegion>("ExecuteCrossRegion", context));
    
    // 创建重复节点
    auto repeat_node = std::make_shared<RepeatNode>("ExecutionRepeat", 1000); // 最多重复1000次
    repeat_node->setChild(execution_sequence);
    
    main_sequence->addChild(repeat_node);
    main_sequence->addChild(std::make_shared<SimpleOutputFinalResult>("OutputResult", context));
    
    return std::make_shared<BehaviorTree>(main_sequence);
}

/**
 * @brief 主函数
 */
int main(int argc, char** argv)
{
    // 解析命令行参数
    CommandLineArgs args = ParseCommandLine(argc, argv);
    
    if (args.show_help)
    {
        PrintUsage(argv[0]);
        return 0;
    }
    
    try
    {
        std::cout << "========== Simple Cross Region Behavior Tree Simulation ==========" << std::endl;
        std::cout << "Scenario Type: " << args.scenario_type << std::endl;
        std::cout << "Simulation Speed: " << args.simulation_speed << "x" << std::endl;
        std::cout << "Debug Output: " << (args.enable_debug ? "Enabled" : "Disabled") << std::endl;
        std::cout << "====================================================================" << std::endl;
        
        // 创建跨区域上下文
        auto context = std::make_shared<CrossRegionContext>();
        context->simulation_speed = args.simulation_speed;
        context->enable_debug_output = args.enable_debug;
        
        // 创建仿真数据生成器并设置场景
        SimulationDataGenerator data_generator;
        data_generator.SetScenario(args.scenario_type);
        
        // 创建行为树
        auto tree = CreateLoopCrossRegionTree(context);
        
        std::cout << "\nStarting behavior tree execution..." << std::endl;
        
        // 记录开始时间
        auto start_time = std::chrono::steady_clock::now();
        
        // 执行行为树
        NodeStatus status = tree->tickWhileRunning(50);
        
        // 记录结束时间
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        std::cout << "\nBehavior tree execution completed!" << std::endl;
        std::cout << "Final Status: " << toStr(status) << std::endl;
        std::cout << "Real Execution Time: " << duration.count() << " ms" << std::endl;
        std::cout << "Simulation Time: " << context->GetSimulationTime() << " seconds" << std::endl;
        
        // 输出最终统计信息
        std::cout << "\n========== Execution Summary ==========" << std::endl;
        std::cout << "Initialized: " << (context->is_initialized.load() ? "YES" : "NO") << std::endl;
        std::cout << "Enabled: " << (context->is_enabled.load() ? "YES" : "NO") << std::endl;
        std::cout << "Completed: " << (context->is_completed.load() ? "YES" : "NO") << std::endl;
        std::cout << "Successful: " << (context->is_successful.load() ? "YES" : "NO") << std::endl;
        std::cout << "Final State: " << asStringLiteral(context->current_state) << std::endl;
        std::cout << "=======================================" << std::endl;
        
        return (status == NodeStatus::SUCCESS) ? 0 : 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
