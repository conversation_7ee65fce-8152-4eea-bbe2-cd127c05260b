#pragma once

#include "../action_node.hpp"
#include "../condition_node.hpp"
#include "cross_region_context.hpp"
#include "simulation_data_generator.hpp"

#include <iostream>
#include <memory>

namespace fescue_iox
{

/**
 * @brief 初始化跨区域算法节点
 */
class InitCrossRegion : public ActionNode<CrossRegionContext>
{
public:
    InitCrossRegion(const std::string& name, const BT::NodeConfig& config)
        : ActionNode<CrossRegionContext>(name, config)
        , data_generator_(std::make_unique<SimulationDataGenerator>())
    {
    }

    void OnStart() override
    {
        auto& ctx = context();
        std::lock_guard<std::mutex> lock(ctx.data_mutex);

        if (ctx.enable_debug_output) {
            std::cout << "[InitCrossRegion] Starting initialization..." << std::endl;
        }

        try {
            // 创建跨区域算法实例
            ctx.cross_region_alg = std::make_shared<NavigationCrossRegionAlg>(ctx.alg_param);

            // 设置回调函数
            ctx.cross_region_alg->SetFeatureSelectCallback([&ctx](const std::vector<FeatureSelectData>& data) {
                (void)data;
                if (ctx.enable_debug_output) {
                    std::cout << "[Callback] Feature select callback triggered" << std::endl;
                }
            });

            ctx.cross_region_alg->SetCrossRegionRunningStateCallback([&ctx](CrossRegionRunningState state) {
                ctx.current_state = state;
                if (ctx.enable_debug_output) {
                    std::cout << "[Callback] Cross region state changed to: " << asStringLiteral(state) << std::endl;
                }
            });

            ctx.cross_region_alg->SetMarkLocationMarkIdCallback([&ctx](int mark_id) -> bool {
                if (ctx.enable_debug_output) {
                    std::cout << "[Callback] Mark location mark id: " << mark_id << std::endl;
                }
                return true;
            });

            // 设置算法运行状态
            ctx.mower_state = MowerRunningState::RUNNING;
            ctx.cross_region_alg->SetAlgoRunningState(ctx.mower_state);

            // 初始化仿真数据
            data_generator_->UpdateSimulationData(ctx, 0.0);

            ctx.is_initialized.store(true);
            ctx.simulation_running.store(true);
            ctx.start_time = std::chrono::steady_clock::now();

            if (ctx.enable_debug_output) {
                std::cout << "[InitCrossRegion] Initialization completed successfully" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "[InitCrossRegion] Initialization failed: " << e.what() << std::endl;
            ctx.is_initialized.store(false);
        }
    }

    BT::NodeStatus OnRunning() override
    {
        auto& ctx = context();

        if (ctx.is_initialized.load()) {
            return BT::NodeStatus::SUCCESS;
        } else {
            return BT::NodeStatus::FAILURE;
        }
    }

private:
    std::unique_ptr<SimulationDataGenerator> data_generator_;
};

/**
 * @brief 检查跨区域准备就绪条件节点
 */
class CheckCrossRegionReady : public ConditionNode<CrossRegionContext>
{
public:
    CheckCrossRegionReady(const std::string& name, const BT::NodeConfig& config)
        : ConditionNode<CrossRegionContext>(name, config)
    {
    }

    BT::NodeStatus OnTick() override
    {
        auto& ctx = context();

        if (!ctx.is_initialized.load()) {
            if (ctx.enable_debug_output) {
                std::cout << "[CheckCrossRegionReady] Not initialized" << std::endl;
            }
            return BT::NodeStatus::FAILURE;
        }

        if (ctx.is_completed.load()) {
            if (ctx.enable_debug_output) {
                std::cout << "[CheckCrossRegionReady] Already completed" << std::endl;
            }
            return BT::NodeStatus::FAILURE;
        }

        // 检查算法实例是否存在
        if (!ctx.cross_region_alg) {
            if (ctx.enable_debug_output) {
                std::cout << "[CheckCrossRegionReady] Algorithm instance not available" << std::endl;
            }
            return BT::NodeStatus::FAILURE;
        }

        // 检查初始条件
        bool initial_conditions_ok = ctx.cross_region_alg->CheckInitialConditions(ctx.fusion_result);

        if (ctx.enable_debug_output) {
            std::cout << "[CheckCrossRegionReady] Ready: " << (initial_conditions_ok ? "YES" : "NO")
                      << ", State: " << asStringLiteral(ctx.current_state) << std::endl;
        }

        return initial_conditions_ok ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
    }
};

/**
 * @brief 启用跨区域算法节点
 */
class EnableCrossRegion : public ActionNode<CrossRegionContext>
{
public:
    EnableCrossRegion(const std::string& name, const BT::NodeConfig& config)
        : ActionNode<CrossRegionContext>(name, config)
    {
    }

    void OnStart() override
    {
        auto& ctx = context();

        if (ctx.enable_debug_output) {
            std::cout << "[EnableCrossRegion] Enabling cross region algorithm..." << std::endl;
        }

        ctx.is_enabled.store(true);

        if (ctx.cross_region_alg) {
            ctx.cross_region_alg->SetVelPublisherProhibit(false);
        }
    }

    BT::NodeStatus OnRunning() override
    {
        auto& ctx = context();
        return ctx.is_enabled.load() ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
    }
};

/**
 * @brief 执行跨区域算法节点
 */
class ExecuteCrossRegion : public ActionNode<CrossRegionContext>
{
public:
    ExecuteCrossRegion(const std::string& name, const BT::NodeConfig& config)
        : ActionNode<CrossRegionContext>(name, config)
        , data_generator_(std::make_unique<SimulationDataGenerator>())
        , execution_count_(0)
    {
    }

    void OnStart() override
    {
        auto& ctx = context();

        if (ctx.enable_debug_output) {
            std::cout << "[ExecuteCrossRegion] Starting cross region execution..." << std::endl;
        }

        execution_count_ = 0;
        last_execution_time_ = std::chrono::steady_clock::now();
    }

    BT::NodeStatus OnRunning() override
    {
        auto& ctx = context();

        if (!ctx.is_enabled.load() || !ctx.cross_region_alg) {
            return BT::NodeStatus::FAILURE;
        }

        if (ctx.is_completed.load()) {
            return BT::NodeStatus::SUCCESS;
        }

        // 更新仿真数据
        double simulation_time = ctx.GetSimulationTime();
        data_generator_->UpdateSimulationData(ctx, simulation_time);
        ctx.UpdateSimulationTime();

        // 执行跨区域算法
        try {
            ctx.last_result = ctx.cross_region_alg->DoCrossRegion(
                ctx.fusion_result,
                ctx.mark_loc_result,
                ctx.imu_data,
                ctx.mcu_exception_status);

            execution_count_++;

            // 检查是否完成
            if (ctx.last_result.cross_region_completed) {
                ctx.is_completed.store(true);
                ctx.is_successful.store(ctx.last_result.cross_region_status == CrossRegionStatus::Successed);

                // 设置最终结果
                ctx.final_result.completed = true;
                ctx.final_result.result = ctx.is_successful.load();
                ctx.final_result.timestamp = GetSteadyClockTimestampMs();

                if (ctx.enable_debug_output) {
                    std::cout << "[ExecuteCrossRegion] Cross region completed! Success: "
                              << ctx.is_successful.load() << std::endl;
                }

                return BT::NodeStatus::SUCCESS;
            }

            // 定期输出状态信息
            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(now - last_execution_time_).count() > 1000) {
                if (ctx.enable_debug_output) {
                    std::cout << "[ExecuteCrossRegion] " << ctx.GetStatusString()
                              << ", Executions: " << execution_count_ << std::endl;
                }
                last_execution_time_ = now;
            }

            return BT::NodeStatus::RUNNING;
        } catch (const std::exception& e) {
            std::cerr << "[ExecuteCrossRegion] Execution failed: " << e.what() << std::endl;
            return BT::NodeStatus::FAILURE;
        }
    }

private:
    std::unique_ptr<SimulationDataGenerator> data_generator_;
    int execution_count_;
    std::chrono::steady_clock::time_point last_execution_time_;
};

/**
 * @brief 检查跨区域完成条件节点
 */
class CheckCrossRegionComplete : public ConditionNode<CrossRegionContext>
{
public:
    CheckCrossRegionComplete(const std::string& name, const BT::NodeConfig& config)
        : ConditionNode<CrossRegionContext>(name, config)
    {
    }

    BT::NodeStatus OnTick() override
    {
        auto& ctx = context();

        bool is_completed = ctx.is_completed.load();

        if (ctx.enable_debug_output && is_completed) {
            std::cout << "[CheckCrossRegionComplete] Cross region completed! Success: "
                      << ctx.is_successful.load() << std::endl;
        }

        return is_completed ? BT::NodeStatus::SUCCESS : BT::NodeStatus::FAILURE;
    }
};

/**
 * @brief 禁用跨区域算法节点
 */
class DisableCrossRegion : public ActionNode<CrossRegionContext>
{
public:
    DisableCrossRegion(const std::string& name, const BT::NodeConfig& config)
        : ActionNode<CrossRegionContext>(name, config)
    {
    }

    void OnStart() override
    {
        auto& ctx = context();

        if (ctx.enable_debug_output) {
            std::cout << "[DisableCrossRegion] Disabling cross region algorithm..." << std::endl;
        }

        ctx.is_enabled.store(false);

        if (ctx.cross_region_alg) {
            ctx.cross_region_alg->SetVelPublisherProhibit(true);
            ctx.cross_region_alg->ResetCrossRegionFlags();
        }
    }

    BT::NodeStatus OnRunning() override
    {
        return BT::NodeStatus::SUCCESS;
    }
};

/**
 * @brief 输出最终结果节点
 */
class OutputFinalResult : public ActionNode<CrossRegionContext>
{
public:
    OutputFinalResult(const std::string& name, const BT::NodeConfig& config)
        : ActionNode<CrossRegionContext>(name, config)
    {
    }

    void OnStart() override
    {
        auto& ctx = context();

        std::cout << "\n========== Cross Region Simulation Final Result ==========" << std::endl;
        std::cout << "Simulation Time: " << ctx.GetSimulationTime() << " seconds" << std::endl;
        std::cout << "Completed: " << (ctx.final_result.completed ? "YES" : "NO") << std::endl;
        std::cout << "Success: " << (ctx.final_result.result ? "YES" : "NO") << std::endl;
        std::cout << "Final State: " << asStringLiteral(ctx.current_state) << std::endl;
        std::cout << "Mower State: " << asStringLiteral(ctx.mower_state) << std::endl;

        if (ctx.final_result.completed) {
            std::cout << "Cross Region Status: " << (ctx.last_result.cross_region_status == CrossRegionStatus::Successed ? "SUCCESS" : ctx.last_result.cross_region_status == CrossRegionStatus::Failed ? "FAILED"
                                                                                                                                                                                                        : "IN_PROGRESS")
                      << std::endl;
        }

        std::cout << "Final Position: (" << ctx.fusion_result.pose.x << ", "
                  << ctx.fusion_result.pose.y << ", " << ctx.fusion_result.pose.yaw << ")" << std::endl;
        std::cout << "========================================================\n"
                  << std::endl;
    }

    BT::NodeStatus OnRunning() override
    {
        return BT::NodeStatus::SUCCESS;
    }
};

/**
 * @brief 重置跨区域上下文节点
 */
class ResetCrossRegion : public ActionNode<CrossRegionContext>
{
public:
    ResetCrossRegion(const std::string& name, const BT::NodeConfig& config)
        : ActionNode<CrossRegionContext>(name, config)
    {
    }

    void OnStart() override
    {
        auto& ctx = context();

        if (ctx.enable_debug_output) {
            std::cout << "[ResetCrossRegion] Resetting cross region context..." << std::endl;
        }

        ctx.Reset();
    }

    BT::NodeStatus OnRunning() override
    {
        return BT::NodeStatus::SUCCESS;
    }
};

} // namespace fescue_iox
