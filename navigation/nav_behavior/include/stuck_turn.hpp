#pragma once

#include "data_type.hpp"
#include "basic_move.hpp"

namespace fescue_iox
{

class StuckTurn : public BasicMove
{
public:
    StuckTurn(const MoveConfig& config) : BasicMove(config) 
    {
    }

    std::string GetName() const override 
    { 
        if (config_.behavior_type == MoveBehaviorType::STUCK_TURN_LEFT)
        {
            return "STUCK_TURN_LEFT";
        }
        else
        {
            return "STUCK_TURN_RIGHT";
        }
    }

    MoveBehaviorType ProcessException(const BehaviorException& exception) override 
    {
        (void)exception;
        return MoveBehaviorType::INVALID;
    }

protected:
    void InitData(const PerceptionFusionResult &fusion_result, const AvoidObsMode& avoid_obs_mode, BehaviorContext& context) override {
        (void)fusion_result;
        (void)avoid_obs_mode;
        (void)context;
        if (config_.behavior_type == MoveBehaviorType::STUCK_TURN_LEFT)
        {
            SetSuccessBehaviorType(MoveBehaviorType::STUCK_TURN_RIGHT);
        }
        else
        {
            SetSuccessBehaviorType(MoveBehaviorType::STUCK_BACKFORWARD);
        }
        // 计算转向角度
        if (config_.behavior_type == MoveBehaviorType::STUCK_TURN_LEFT)
        {
            turn_angle_ = config_.default_turn_angle;
        }
        else 
        {
            turn_angle_ = -config_.default_turn_angle;
        }
        turn_duration_ms_ = config_.move_time;
        LOG_INFO("turn_angle: {}, turn_duration_ms: {}", turn_angle_, turn_duration_ms_);
    }

    bool IsFinished(const Pose2f& pose) override
    {
        float angle_diff = NormalizeAngle(pose.theta - start_pose_.theta);
        if (std::abs(angle_diff) > std::abs(turn_angle_))
        {
            LOG_INFO("turn finished name: {} angle_diff: {}", GetName(), angle_diff);
            return true;
        }
        return false;
    }

    bool IsFailed(BehaviorContext& context) override
    {
        (void)context;
        if (!config_.check_timeout)
        {
            return false;
        }
        uint64_t time_now_ms = GetSteadyClockTimestampMs();
        if (time_now_ms > timeout_start_time_ && time_now_ms - timeout_start_time_ > config_.timeout_duration_ms)
        {
            LOG_INFO("stuck turn timeout name: {} time_now_ms: {}", GetName(), time_now_ms);
            context.error_type = BehaviorErrorType::STRONG_RECOVERY_FAILED;
            return true;
        }
        return false;
    }
};

}