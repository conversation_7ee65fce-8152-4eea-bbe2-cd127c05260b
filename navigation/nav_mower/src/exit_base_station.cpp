#include "exit_base_station.hpp"

#include "utils/time.hpp"
#include "utils/logger.hpp"

namespace fescue_iox
{

ExitBaseStationResult ExitBaseStationAlg::Run(bool is_power_connected, bool is_on_grass_field, const std::chrono::seconds &non_grass_duration,
                                              const QRCodeLocationResult &qrcode_loc_result)
{
    ExitBaseStationResult result;
    if (state_ == ExitBaseStationState::FINISH)
    {
        result.is_need_exit = false;
        return result;
    }
    result.is_need_exit = true;
    uint64_t time_now_ms = GetSteadyClockTimestampMs();
    if (state_ == ExitBaseStationState::INVALID)
    {
        state_ = ExitBaseStationState::WAIT;
        LOG_INFO("exit base station switch to wait");
    }
    if (state_ == ExitBaseStationState::WAIT)
    {
        result.linear_velocity = 0.0;
        result.angular_velocity = 0.0;
        if (wait_start_time_ms_ == 0)
        {
            wait_start_time_ms_ = time_now_ms;
            LOG_INFO("exit base station start to wait");
        }
        uint64_t diff_time_ms = time_now_ms - wait_start_time_ms_;
        if (diff_time_ms >= config_.wait_time_ms)
        {
            if (!IsOnGrass(is_power_connected, is_on_grass_field, non_grass_duration))
            {
                state_ = ExitBaseStationState::BACK_TO_GRASS;
                LOG_INFO("exit base station switch to back to grass power: {} grass: {} duration: {}", 
                         is_power_connected, is_on_grass_field, non_grass_duration.count());
            }
            else if (is_power_connected)
            {
                state_ = ExitBaseStationState::EXIT_FROM_BASE_STATION;
                LOG_INFO("exit base station switch to exit from base station power: {} grass: {} duration: {}", 
                         is_power_connected, is_on_grass_field, non_grass_duration.count());
            }
            else
            {
                state_ = ExitBaseStationState::EXIT_BY_DISTANCE;
                LOG_INFO("exit base station switch to exit by distance power: {} grass: {} duration: {}", 
                         is_power_connected, is_on_grass_field, non_grass_duration.count());
            }
        }
        LOG_INFO("exit base station wait linear: {} angular: {}", result.linear_velocity, result.angular_velocity);
    }
    if (state_ == ExitBaseStationState::BACK_TO_GRASS)
    {
        result.linear_velocity = config_.back_to_grass_linear_velocity;
        result.angular_velocity = config_.back_to_grass_angular_velocity;
        if (back_to_grass_start_time_ms_ == 0)
        {
            back_to_grass_start_time_ms_ = time_now_ms;
            LOG_INFO("exit base station start to back to grass");
        }
        uint64_t diff_time_ms = time_now_ms - back_to_grass_start_time_ms_;
        if (IsOnGrass(is_power_connected, is_on_grass_field, non_grass_duration))
        {
            if (is_power_connected)
            {
                state_ = ExitBaseStationState::EXIT_FROM_BASE_STATION;
                LOG_INFO("exit base station switch to exit from base station power: {} grass: {} duration: {}", 
                         is_power_connected, is_on_grass_field, non_grass_duration.count());
            }
            else
            {
                state_ = ExitBaseStationState::EXIT_BY_DISTANCE;
                LOG_INFO("exit base station switch to exit by distance power: {} grass: {} duration: {}", 
                         is_power_connected, is_on_grass_field, non_grass_duration.count());
            }
        }
        else if (diff_time_ms >= config_.back_to_grass_time_ms)
        {
            result.linear_velocity = 0.0;
            result.angular_velocity = 0.0;
            result.is_need_publish_exception = true;
            result.exception_level = mower_msgs::msg::SocExceptionLevel::ERROR;
            result.exception_value = mower_msgs::msg::SocExceptionValue::ALG_PNC_SELF_CHECK_FAILED_EXCEPTION;
            state_ = ExitBaseStationState::INVALID;
            LOG_ERROR("exit base station back to grass timeout power: {} grass: {} duration: {}", 
                     is_power_connected, is_on_grass_field, non_grass_duration.count());
        }
        LOG_INFO("exit base station back to grass linear: {} angular: {}", result.linear_velocity, result.angular_velocity);
    }
    if (state_ == ExitBaseStationState::EXIT_FROM_BASE_STATION)
    {
        result.linear_velocity = config_.back_from_base_station_linear_velocity;
        result.angular_velocity = config_.back_from_base_station_angular_velocity;
        if (exit_from_base_station_start_time_ms_ == 0)
        {
            exit_from_base_station_start_time_ms_ = time_now_ms;
            LOG_INFO("exit base station start to exit from base station");
        }
        uint64_t diff_time_ms = time_now_ms - exit_from_base_station_start_time_ms_;
        if (diff_time_ms >= config_.back_from_base_station_time_ms)
        {
            state_ = ExitBaseStationState::TURN_ANGLE;
            LOG_INFO("exit base station switch to turn angle");
        }
        LOG_INFO("exit base station exit from base station linear: {} angular: {}", result.linear_velocity, result.angular_velocity);
    }
    if (state_ == ExitBaseStationState::EXIT_BY_DISTANCE)
    {
        result.linear_velocity = config_.back_by_distance_linear_velocity;
        result.angular_velocity = config_.back_by_distance_angular_velocity;
        if (exit_by_distance_start_time_ms_ == 0)
        {
            exit_by_distance_start_time_ms_ = time_now_ms;
            LOG_INFO("exit base station start to exit by distance");
        }
        uint64_t diff_time_ms = time_now_ms - exit_by_distance_start_time_ms_;
        float mower_start_qr_distance = sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2));
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE && 
            mower_start_qr_distance < config_.near_base_station_distance && 
            diff_time_ms < config_.exit_by_distance_time_ms)
        {
            LOG_INFO("exit base station exit by distance linear: {} angular: {}", result.linear_velocity, result.angular_velocity);
        }
        else 
        {
            LOG_INFO("exit base station exit by distance status: {} distance: {} time: {}", 
                     static_cast<int>(qrcode_loc_result.detect_status), mower_start_qr_distance, diff_time_ms);
            state_ = ExitBaseStationState::CHECK_GRASS;
            LOG_INFO("exit base station switch to check grass");
        }
    }
    if (state_ == ExitBaseStationState::TURN_ANGLE)
    {
        result.linear_velocity = config_.turn_angle_linear_velocity;
        result.angular_velocity = config_.turn_angle_angular_velocity;
        if (turn_angle_start_time_ms_ == 0)
        {
            turn_angle_start_time_ms_ = time_now_ms;
            LOG_INFO("exit base station start to turn angle");
        }
        uint64_t diff_time_ms = time_now_ms - turn_angle_start_time_ms_;
        if (diff_time_ms >= config_.turn_angle_time_ms)
        {
            state_ = ExitBaseStationState::CHECK_GRASS;
            LOG_INFO("exit base station switch to check grass");
        }
        LOG_INFO("exit base station turn angle linear: {} angular: {}", result.linear_velocity, result.angular_velocity);
    }
    if (state_ == ExitBaseStationState::CHECK_GRASS)
    {
        result.linear_velocity = config_.check_grass_linear_velocity;
        result.angular_velocity = config_.check_grass_angular_velocity;
        if (check_grass_start_time_ms_ == 0)
        {
            check_grass_start_time_ms_ = time_now_ms;
            LOG_INFO("exit base station start to check grass");
        }
        uint64_t diff_time_ms = time_now_ms - check_grass_start_time_ms_;
        if (diff_time_ms >= config_.check_grass_time_ms)
        {
            state_ = ExitBaseStationState::FINISH;
            if (is_on_grass_field)
            {
                result.is_exit_success = true;
                result.linear_velocity = 0.0;
                result.angular_velocity = 0.0;
                LOG_INFO("exit base station succeed");
            }
            else 
            {
                result.is_exit_success = false;
                result.linear_velocity = 0.0;
                result.angular_velocity = 0.0;
                result.is_need_publish_exception = true;
                result.exception_level = mower_msgs::msg::SocExceptionLevel::ERROR;
                result.exception_value = mower_msgs::msg::SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION;
                LOG_INFO("exit base station failed, not on grass");
            }
        }
        LOG_INFO("exit base station check grass linear: {} angular: {}", result.linear_velocity, result.angular_velocity);
    }
    
    return result;
}

bool ExitBaseStationAlg::IsOnGrass(bool is_power_connected, bool is_on_grass_field, const std::chrono::seconds &non_grass_duration) const
{
    if (!is_power_connected && !is_on_grass_field && non_grass_duration.count() > config_.non_grass_duration_s)
    {
        return false;
    }
    return true;
}

void ExitBaseStationAlg::Reset()
{
    state_ = ExitBaseStationState::INVALID;
    wait_start_time_ms_ = 0;
    back_to_grass_start_time_ms_ = 0;
    exit_from_base_station_start_time_ms_ = 0;
    exit_by_distance_start_time_ms_ = 0;
    turn_angle_start_time_ms_ = 0;
    check_grass_start_time_ms_ = 0;
}

} // namespace fescue_iox