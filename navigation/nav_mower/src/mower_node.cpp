#include "mower_node.hpp"

#include "json.hpp"
#include "mower_msgs/srv/camera_calibration_params.hpp"
#include "mower_node_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "slope_control_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/string_utils.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <iostream>
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

std::string GetCurrentTimeString()
{
    // Get the current time point
    auto now = std::chrono::system_clock::now();
    // Convert to time_t (second precision) and tm struct
    auto timer = std::chrono::system_clock::to_time_t(now);
    std::tm bt = *std::localtime(&timer); // Note: localtime is not thread-safe
    // Extract milliseconds part
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                  now.time_since_epoch()) %
              1000;
    // Format output
    std::ostringstream oss;
    oss << std::put_time(&bt, "%Y-%m-%d_%H-%M-%S");                // Year-Month-Day_Hour-Minute-Second
    oss << '.' << std::setfill('0') << std::setw(3) << ms.count(); // Milliseconds

    return oss.str();
}

NavigationMowerNode::NavigationMowerNode(const std::string &node_name)
    : node_name_(node_name)

{
    last_mcu_exception_time_ = std::chrono::steady_clock::now();
    last_behavior_timeout_time_ = std::chrono::steady_clock::now();
    stuck_detection_state_change_time_ = std::chrono::steady_clock::now();

    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitHeartbeat();
    InitAlgorithm();
    InitTaskStateByMCUMissionType();
    SendAlgorithmVersion();
}

NavigationMowerNode::~NavigationMowerNode()
{
    DeinitAlgorithm();
    CloseImuDataFile();
    last_mcu_exception_time_ = std::chrono::steady_clock::now();
    last_behavior_timeout_time_ = std::chrono::steady_clock::now();
    LOG_WARN("NavigationMowerNode exit!");
}

void NavigationMowerNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationMowerNode::InitParam()
{
    const std::string conf_file{"conf/navigation_mower_node/navigation_mower_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationMowerNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationMowerNode create config path failed!!!");
        }
    }
    if (!Config<NavigationMowerNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationMowerNode config parameters failed!");
    }
    NavigationMowerNodeConfig config = Config<NavigationMowerNodeConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    chassis_type_ = config.chassis_type;
    mower_alg_conf_file_ = config.mower_alg_conf_file;
    slope_ctrl_alg_conf_file_ = config.slope_ctrl_alg_conf_file;

    if (!Config<NavigationMowerNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set NavigationMowerNodeConfig config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void NavigationMowerNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationMowerNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationMowerNode::InitAlgorithmParam()
{
    std::string conf_path = GetDirectoryPath(mower_alg_conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Mower algo create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Mower algo create config path failed!!!");
        }
    }
    if (!Config<NavigationMowerAlgConfig>::Init(mower_alg_conf_file_))
    {
        LOG_WARN("Init Mower algo config parameters failed!");
    }
    NavigationMowerAlgConfig config = Config<NavigationMowerAlgConfig>::GetConfig();

    LOG_INFO("[navigation_mower_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_mower_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_mower_node] compile time: {}", _COMPILE_TIME_);

    LOG_INFO("{}", config.toString().c_str());
    ConfigParamToAlgorithmParam(config, mower_alg_param_);
    if (!Config<NavigationMowerAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Mower algo config parameters failed!");
    }
}

void NavigationMowerNode::InitSlopeCtrlAlgorithmParam()
{
    std::string conf_path = GetDirectoryPath(slope_ctrl_alg_conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Mower node create slope ctrl alg config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Mower node create slope ctrl alg config path failed!!!");
        }
    }
    if (!Config<NavigationSlopeControlAlgConfig>::Init(slope_ctrl_alg_conf_file_))
    {
        LOG_WARN("Init slope ctrl algo config parameters failed!");
    }
    NavigationSlopeControlAlgConfig config = Config<NavigationSlopeControlAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    slope_ctrl_alg_param_.k_linear = config.k_linear;
    slope_ctrl_alg_param_.k_angular = config.k_angular;
    if (!Config<NavigationSlopeControlAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set slope ctrl algo config parameters failed!");
    }
}

void NavigationMowerNode::InitAlgorithm()
{
    InitAlgorithmParam();
    InitSlopeCtrlAlgorithmParam();

    slope_ctrl_alg_ = std::make_unique<NavigationSlopeControlAlg>(slope_ctrl_alg_param_);

    mower_alg_ = std::make_unique<NavigationMowerAlg>(mower_alg_param_);

    mower_alg_->SetFeatureSelectCallback([this](const std::vector<FeatureSelectData> &data) -> void {
        fescue_msgs__msg__NavigationAlgoCtrlData nav_alg_ctrl_data;
        if (this->ConvertFeatureSelectToNavAlgCtrlData(data, nav_alg_ctrl_data))
        {
            this->DealFeatureSelectCallback(nav_alg_ctrl_data);
        }
    });
    mower_alg_->SetMarkLocationMarkIdCallback([this](int mark_id) -> bool {
        return this->DealMarkLocationMarkIdCallback(mark_id);
    });
    mower_alg_->SetCrossRegionRunningStateCallback([this](CrossRegionRunningState state) -> void {
        this->DealCrossRegionRunningStateCallback(state);
    });

    mower_alg_->SetUndockResultCallback([this](bool completed, bool result, mower_msgs::srv::UndockOperationStatus status) -> void {
        this->DealUndockFinalResult(completed, result, status);
    });

    mower_alg_->SetAreaCalcStartCallback([this](uint64_t timestamp) -> bool {
        return this->DealRegionExploreAreaCalcStart(timestamp);
    });

    mower_alg_->SetAreaCalcStopCallback([this](uint64_t timestamp, float &area, float &perimeter) -> bool {
        return this->DealRegionExploreAreaCalcStop(timestamp, area, perimeter);
    });

    mower_alg_->SetRegionExploreResultCallback([this](RegionExploreResult &result) -> void {
        this->DealRegionExploreResult(result);
    });

    mower_alg_->SetCutBorderResultCallback([this](bool completed, bool result) -> void {
        this->DealCutBorderResult(completed, result);
    });

    mower_alg_->SetEdgeFollowStatusCallback([this](int status) -> void {
        this->DealEdgeFollowStatusCallback(status);
    });

    InitCalibResult();

    thread_running_.store(true);
    mower_thread_ = std::thread(&NavigationMowerNode::MowerThread, this);
}

void NavigationMowerNode::DeinitAlgorithm()
{
    if (mower_alg_)
    {
        mower_alg_->ProhibitVelPublisher();
    }

    thread_running_.store(false);
    if (mower_thread_.joinable())
    {
        mower_thread_.join();
    }
}

void NavigationMowerNode::InitSubscriber()
{
    sub_mark_loc_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>>(
        "mark_location_result", 1, [this](const fescue_msgs__msg__MarkLocationResult &data, const std::string &event) {
            (void)event;
            DealMarkLocationResult(data);
        });

    sub_cross_region_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>>(
        "navigation_cross_region_state", 10, [this](const fescue_msgs__msg__CrossRegionStateData &data, const std::string &event) {
            (void)event;
            DealCrossRegionState(data);
        });

    sub_cross_region_state_mower_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>>(
        "navigation_cross_region_state_mower", 10, [this](const fescue_msgs__msg__CrossRegionStateData &data, const std::string &event) {
            (void)event;
            DealCrossRegionState(data);
        });

    sub_recharge_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__RechargeStateData>>(
        "navigation_recharge_state", 10, [this](const fescue_msgs__msg__RechargeStateData &data, const std::string &event) {
            (void)event;
            DealRechargeState(data);
        });

    sub_behavior_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__BehaviorStateData>>(
        "navigation_behavior_state", 10, [this](const fescue_msgs__msg__BehaviorStateData &data, const std::string &event) {
            (void)event;
            DealBehaviorState(data);
        });

    sub_random_mower_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__RandomMowerStateData>>(
        "navigation_random_mower_state", 10, [this](const fescue_msgs__msg__RandomMowerStateData &data, const std::string &event) {
            (void)event;
            DealRandomMowerState(data);
        });

    sub_nav_twist_ = std::make_unique<IceoryxSubscriberMower<geometry_msgs__msg__Twist_iox>>(
        "navigation_mower_twist", 1, [this](const geometry_msgs__msg__Twist_iox &data, const std::string &event) {
            (void)event;
            DealMowerTwist(data);
        });

    sub_perception_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 1, [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            (void)event;
            DealPerceptionFusionResult(data);
        });

    sub_qrcode_loc_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__QrCodeResult>>(
        "detect_qrcode_result", 1, [this](const fescue_msgs__msg__QrCodeResult &data, const std::string &event) {
            (void)event;
            DealQRCodeLocationResult(data);
        });

    sub_recharge_final_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavRechargeFinalResult>>(
        "navigation_recharge_final_result", 10, [this](const fescue_msgs__msg__NavRechargeFinalResult &data, const std::string &event) {
            (void)event;
            DealRechargeFinalResult(data);
        });

    sub_cross_region_final_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavCrossRegionFinalResult>>(
        "navigation_cross_region_final_result", 10, [this](const fescue_msgs__msg__NavCrossRegionFinalResult &data, const std::string &event) {
            (void)event;
            DealCrossRegionFinalResult(data);
        });

    sub_spiral_mower_final_result_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::NavSpiralMowerFinalResult>>(
        "navigation_spiral_mower_final_result", 10, [this](const ob_mower_msgs::NavSpiralMowerFinalResult &data, const std::string &event) {
            (void)event;
            DealSpiralMowerFinalResult(data);
        });

    sub_mcu_sensor_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuSensor>>(
        "mcu_sensor", 1, [this](const mower_msgs::msg::McuSensor &data, const std::string &event) {
            (void)event;
            DealMCUSensor(data);
        });

    sub_mcu_exception_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuException>>(
        "mcu_exception", 10, [this](const mower_msgs::msg::McuException &data, const std::string &event) {
            (void)event;
            DealMCUException(data);
        });

#if 0
    sub_mcu_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuImu>>(
        "mcu_imu", 1, [this](const mower_msgs::msg::McuImu &data, const std::string &event) {
            (void)event;
            DealMcuImu(data);
        });
#else
    sub_soc_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::SocImu>>(
        "soc_imu", 1, [this](const mower_msgs::msg::SocImu &data, const std::string &event) {
            (void)event;
            DealSocImu(data);
        });
#endif

    // sub_mcu_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuImu>>(
    //     "mcu_imu", 1, [this](const mower_msgs::msg::McuImu &data, const std::string &event) {
    //         (void)event;
    //         DealMcuImu(data);
    //     });
    // sub_soc_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::SocImu>>(
    //     "soc_imu", 1, [this](const mower_msgs::msg::SocImu &data, const std::string &event) {
    //         (void)event;
    //         DealSocImu(data);
    //     });

    sub_mcu_motor_speed_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>>(
        "mcu_motor_speed", 1, [this](const mower_msgs::msg::McuMotorSpeed &data, const std::string &event) {
            (void)event;
            DealMcuMotorSpeed(data);
        });

    sub_motion_detection_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__LocalizationMotionDetectionResult>>(
        "localization_motion_detection_result", 1, [this](const fescue_msgs__msg__LocalizationMotionDetectionResult &data, const std::string &event) {
            (void)event;
            DealMotionDetectionResult(data);
        });

    sub_loc_slope_detection_result_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::LawnmowerSlopeStatus>>(
        "localization_slope_detection_result", 1, [this](const mower_msgs::msg::LawnmowerSlopeStatus &data, const std::string &event) {
            (void)event;
            DealSlopeDetectionResult(data);
        });
    sub_charge_mark_detect_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__ChargeMarkDetectResult>>(
        "charge_mark_detect_result", 1, [this](const fescue_msgs__msg__ChargeMarkDetectResult &data, const std::string &event) {
            (void)event;
            DealChargeDetectResult(data.charge_result);
        });

    sub_function_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs_FunctionStateData>>(
        "navigation_function_state", 10, [this](const fescue_msgs_FunctionStateData &data, const std::string &event) {
            (void)event;
            DealFunctionState(data);
        });
    sub_random_mower_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_random_mower_nav_alg_ctrl", 10, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealOtherTaskNavAlgCtrl(data);
        });
    sub_edge_follow_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_edge_follow_nav_alg_ctrl", 10, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealOtherTaskNavAlgCtrl(data);
        });
    sub_cross_region_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_cross_region_nav_alg_ctrl", 10, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealOtherTaskNavAlgCtrl(data);
        });
}

void NavigationMowerNode::InitPublisher()
{
    pub_nav_alg_ctrl_ = std::make_unique<IceoryxPublisherMower<fescue_msgs__msg__NavigationAlgoCtrlData>>("navigation_nav_alg_ctrl");
    pub_cross_region_state_mower_ = std::make_unique<IceoryxPublisherMower<fescue_msgs__msg__CrossRegionStateData>>("navigation_cross_region_state_mower");
    pub_nav_running_state_ = std::make_unique<IceoryxPublisherMower<fescue_msgs__msg__NavigationRunningStateData>>("navigation_running_state");
    pub_nav_fusion_pose_ = std::make_unique<IceoryxPublisherMower<ob_mower_msgs::NavFusionPose>>("navigation_fusion_pose");
    pub_nav_dangerous_point_cloud_ = std::make_unique<IceoryxPublisherMower<ob_mower_msgs::NavPointCloud>>("navigation_dangerous_point_cloud");
    pub_nav_twist_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::Twist>>("mcu_twist");
    pub_beacon_status_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::BeaconStatus>>("beacon_status");
    pub_perception_localization_alg_ctrl_ = std::make_unique<IceoryxPublisherMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>("perception_localization_alg_ctrl");
    pub_edge_follow_running_status_ = std::make_unique<IceoryxPublisherMower<std_msgs__msg__Int32_iox>>("edge_follow_running_status");
}

void NavigationMowerNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_navigation_mower_node_param_request", 10,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetMowerNodeParam(response.data);
            LOG_INFO("Get navigation mower node param request execute {}!", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_navigation_mower_node_param_request", 10,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetMowerNodeParam(request.data);
            LOG_INFO("Set navigation mower node param request execute {}!", response.success);
        });
    service_sw_go_mower_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoMowRequest, mower_msgs::srv::GoMowResponse>>(
        "go_to_mow", 10,
        [this](const mower_msgs::srv::GoMowRequest &request, mower_msgs::srv::GoMowResponse &response) {
            response.success = DealSWMowerRequest(request.request_type);
            LOG_INFO("Set navigation mower go mower request execute {}!", response.success);
        });
    service_sw_go_charge_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoChargeRequest, mower_msgs::srv::GoChargeResponse>>(
        "go_to_charge", 10,
        [this](const mower_msgs::srv::GoChargeRequest &request, mower_msgs::srv::GoChargeResponse &response) {
            response.success = DealSWChargeRequest(request.request_type);
            LOG_INFO("Set navigation mower go charge request execute {}!", response.success);
        });
    service_sw_go_cross_region_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoToCrossRegionRequest, mower_msgs::srv::GoToCrossRegionResponse>>(
        "go_to_cross_region", 10,
        [this](const mower_msgs::srv::GoToCrossRegionRequest &request, mower_msgs::srv::GoToCrossRegionResponse &response) {
            response.success = DealSWGoCrossRegionRequest(request.request_type);
            LOG_INFO("Set navigation mower go cross region request execute {}!", response.success);
        });
    service_sw_go_standby_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoToStandbyRequest, mower_msgs::srv::GoToStandbyResponse>>(
        "go_to_standby", 10,
        [this](const mower_msgs::srv::GoToStandbyRequest &request, mower_msgs::srv::GoToStandbyResponse &response) {
            response.success = DealSWStandByRequest(request.request_type);
            LOG_INFO("Set navigation mower go standby request execute {}!", response.success);
        });
    service_sw_explore_map_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::ExploreMapRequest, mower_msgs::srv::ExploreMapResponse>>(
        "explore_map", 10,
        [this](const mower_msgs::srv::ExploreMapRequest &request, mower_msgs::srv::ExploreMapResponse &response) {
            response.success = DealSWRegionExploreRequest(request.request_type);
            LOG_INFO("Set navigation mower region explore request execute {}!", response.success);
        });
    service_sw_go_cut_edge_ = std::make_unique<IceoryxServerPlanning<mower_msgs::srv::GoToCutEdgeRequest, mower_msgs::srv::GoToCutEdgeResponse>>(
        "go_to_cut_edge", 10,
        [this](const mower_msgs::srv::GoToCutEdgeRequest &request, mower_msgs::srv::GoToCutEdgeResponse &response) {
            response.success = DealSWCutBorderRequest(request.request_type);
            LOG_INFO("Set navigation mower cut edge request execute {}!", response.success);
        });
}

void NavigationMowerNode::InitTaskStateByMCUMissionType()
{
    mower_msgs::srv::MowerMissionType mission_type{mower_msgs::srv::MowerMissionType::UNKNOWN};
    if (GetMCUMissionType(mission_type))
    {
        LOG_INFO("Current MCU mission type is {}", mower_msgs::srv::asStringLiteral(mission_type).c_str());
        if (mission_type == mower_msgs::srv::MowerMissionType::POWER_SAVING_STANDBY)
        {
            is_recharge_start_ = false;
            is_cross_region_start_ = false;
            is_random_mower_start_ = false;
            is_region_explore_start_ = false;
            is_spiral_mower_start_ = false;
            is_cut_border_start_ = false;
            LOG_WARN("Current MCU mission type is POWER_SAVING_STANDBY, close all tasks!");
            CloseAllTask();
            SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::DISABLE);
        }
    }
}

void NavigationMowerNode::InitCalibResult()
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraCalibParamsRequest, mower_msgs::srv::CameraCalibParamsResponse>>("get_union_rgb_camera_all_params");
    mower_msgs::srv::CameraCalibParamsRequest request;
    mower_msgs::srv::CameraCalibParamsResponse response;
    if (!client->SendRequest(request, response))
    {
        LOG_ERROR("Get calib all param error!");
        // todo pub exception
        return;
    }
    if (!response.success_imu)
    {
        LOG_ERROR("Get imu calib param from camera node error!");
        // todo pub exception
        return;
    }
    if (mower_alg_)
    {
        IMUCalibrationConfig conf;
        conf.qx = response.imu_params.rot_front_imu_qx;
        conf.qy = response.imu_params.rot_front_imu_qy;
        conf.qz = response.imu_params.rot_front_imu_qz;
        conf.qw = response.imu_params.rot_front_imu_qw;
        conf.acc_bias_x = response.imu_params.acc_bias_x;
        conf.acc_bias_y = response.imu_params.acc_bias_y;
        conf.acc_bias_z = response.imu_params.acc_bias_z;
        conf.gyro_bias_x = response.imu_params.gyr_bias_x;
        conf.gyro_bias_y = response.imu_params.gyr_bias_y;
        conf.gyro_bias_z = response.imu_params.gyr_bias_z;
        mower_alg_->SetImuCalibrationConfig(conf);
    }
    else
    {
        LOG_WARN("Mower_alg is nullptr, set imu calib result error!");
    }
}

void NavigationMowerNode::DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg)
{
    std::lock_guard<std::mutex> lock(fusion_mutex_);
    GetFusionGrassDetectStatus(msg, fusion_result_.grass_detecte_status);
    GetFusionObstacleResult(msg, fusion_result_.obstacle_result);
    GetFusionBoundaryResult(msg, fusion_result_.boundary_result);
    GetFusionSemanticResult(msg, fusion_result_.occupancy_grid);
    fusion_result_.mower_point.x = msg.mower_point.x;
    fusion_result_.mower_point.y = msg.mower_point.y;
    fusion_result_.min_dist_point.x = msg.min_dist_point.x;
    fusion_result_.min_dist_point.y = msg.min_dist_point.y;
    fusion_result_.input_timestamp = msg.timestamp;
    fusion_result_.output_timestamp = msg.output_timestamp;
    is_new_fusion_result_record_ = true;
    is_new_fusion_ = true;

    if (mower_alg_)
    {
        mower_alg_->SetPerceptionFusionResult(fusion_result_);
        mower_alg_->SetGrassDetecteStatus(fusion_result_.grass_detecte_status);
    }
}

void NavigationMowerNode::DealMCUException(const mower_msgs::msg::McuException &data)
{
    std::lock_guard<std::mutex> lock(mcu_exception_mutex_);
    last_mcu_exception_time_ = std::chrono::steady_clock::now();

    if (data.exception_value != mower_msgs::msg::McuExceptionValue::NO_EXCEPTION)
    {
        LOG_INFO("[DealMCUException1] {}", mower_msgs::msg::asStringLiteral(data.exception_value).c_str());
    }

    switch (data.exception_value)
    {
    case mower_msgs::msg::McuExceptionValue::COLLISION_BELOW_3_SECOND_EXCEPTION: // collision below 3 seconds Action: reverse, turn and continue moving
    {
        LOG_WARN("[DealMCUException1] collision below 3 seconds. Action: reverse, turn and continue moving");
        mcu_exception_status_ = McuExceptionStatus::COLLISION;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::COLLISION_ABOVE_3_SECOND_EXCEPTION: // collision above 3 seconds Action: drive motor reverses, then turns to avoid obstacle
    {
        LOG_WARN("[DealMCUException1] collision above 3 seconds. Action: drive motor reverses, then turns to avoid obstacle");
        mcu_exception_status_ = McuExceptionStatus::COLLISION;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::SINGLE_LIFT_SENSOR_TRIGGER_0_3_TO_3_SECONDS_EXCEPTION: // single lift sensor trigger 0.3-3 seconds Action: drive motor keeps original state
    {
        LOG_WARN("[DealMCUException1] single lift sensor triggered 0.3-3 seconds. Action: drive motor keeps original state");
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::SINGLE_LIFT_SENSOR_TRIGGER_3_TO_10_SECONDS_EXCEPTION: // single lift sensor trigger 3-10 seconds Action: stop mowing motor, drive motor reverses to try to release lift
    {
        LOG_WARN("[DealMCUException1] single lift sensor triggered 3-10 seconds. Action: stop mowing motor, drive motor reverses to try to release lift");
        mcu_exception_status_ = McuExceptionStatus::LIFTING;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::DOUBLE_LIFT_SENSOR_TRIGGER_BELOW_10_SECONDS_EXCEPTION: // double lift sensor trigger below 10 seconds Action: stop mowing motor, drive motor reverses to try to release lift
    {
        LOG_WARN("[DealMCUException1] double lift sensor triggered below 10 seconds. Action: stop mowing motor, drive motor reverses to try to release lift");
        mcu_exception_status_ = McuExceptionStatus::LIFTING;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::NO_EXCEPTION:
    {
        LOG_DEBUG("[DealMCUException1] no exception");
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        break;
    }

    default:
        LOG_DEBUG("[DealMCUException1] No safety abnormalities");
        mcu_exception_status_ = McuExceptionStatus::UNKNOWN;
        break;
    }
}

void NavigationMowerNode::DealMcuImu(const mower_msgs::msg::McuImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);

    if (enable_imu_filter_)
    {
        filter_state_.accel_x = LowPassFilter(data.linear_acceleration_x, filter_state_.accel_x, alpha_imu_);
        filter_state_.accel_y = LowPassFilter(data.linear_acceleration_y, filter_state_.accel_y, alpha_imu_);
        filter_state_.accel_z = LowPassFilter(data.linear_acceleration_z, filter_state_.accel_z, alpha_imu_);
        filter_state_.gyro_x = LowPassFilter(data.angular_velocity_x, filter_state_.gyro_x, alpha_imu_);
        filter_state_.gyro_y = LowPassFilter(data.angular_velocity_y, filter_state_.gyro_y, alpha_imu_);
        filter_state_.gyro_z = LowPassFilter(data.angular_velocity_z, filter_state_.gyro_z, alpha_imu_);

        imu_data_.linear_acceleration_x = filter_state_.accel_x;
        imu_data_.linear_acceleration_y = filter_state_.accel_y;
        imu_data_.linear_acceleration_z = filter_state_.accel_z;
        imu_data_.angular_velocity_x = filter_state_.gyro_x;
        imu_data_.angular_velocity_y = filter_state_.gyro_y;
        imu_data_.angular_velocity_z = filter_state_.gyro_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;

        // For the case where filtering is not enabled, fill the filtered data columns with raw data
        if (save_imu_data_.load())
        {
            WriteImuDataFile(data);
        }

        SetImuData(imu_data_);
    }
    else
    {
        imu_data_.angular_velocity_x = data.angular_velocity_x;
        imu_data_.angular_velocity_y = data.angular_velocity_y;
        imu_data_.angular_velocity_z = data.angular_velocity_z;
        imu_data_.linear_acceleration_x = data.linear_acceleration_x;
        imu_data_.linear_acceleration_y = data.linear_acceleration_y;
        imu_data_.linear_acceleration_z = data.linear_acceleration_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;
        // update IMU data

        // For the case where filtering is not enabled, fill the filtered data columns with raw data
        if (save_imu_data_.load())
        {
            WriteImuDataFile(data);
        }

        SetImuData(imu_data_);
    }
}

void NavigationMowerNode::DealSocImu(const mower_msgs::msg::SocImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);

    float angular_velocity_z = -data.angular_velocity_z;

    if (enable_imu_filter_)
    {
        filter_state_.accel_x = LowPassFilter(data.linear_acceleration_x, filter_state_.accel_x, alpha_imu_);
        filter_state_.accel_y = LowPassFilter(data.linear_acceleration_y, filter_state_.accel_y, alpha_imu_);
        filter_state_.accel_z = LowPassFilter(data.linear_acceleration_z, filter_state_.accel_z, alpha_imu_);
        filter_state_.gyro_x = LowPassFilter(data.angular_velocity_x, filter_state_.gyro_x, alpha_imu_);
        filter_state_.gyro_y = LowPassFilter(data.angular_velocity_y, filter_state_.gyro_y, alpha_imu_);
        filter_state_.gyro_z = LowPassFilter(angular_velocity_z, filter_state_.gyro_z, alpha_imu_);

        imu_data_.linear_acceleration_x = filter_state_.accel_x;
        imu_data_.linear_acceleration_y = filter_state_.accel_y;
        imu_data_.linear_acceleration_z = filter_state_.accel_z;
        imu_data_.angular_velocity_x = filter_state_.gyro_x;
        imu_data_.angular_velocity_y = filter_state_.gyro_y;
        imu_data_.angular_velocity_z = filter_state_.gyro_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;

#if 0
        if (imu_data_file_.is_open())
        {
            imu_data_file_ << data.system_timestamp << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << angular_velocity_z << ","
                           << filter_state_.accel_x << ","
                           << filter_state_.accel_y << ","
                           << filter_state_.accel_z << ","
                           << filter_state_.gyro_x << ","
                           << filter_state_.gyro_y << ","
                           << filter_state_.gyro_z << std::endl;
        }

#endif

        SetImuData(imu_data_);
    }
    else
    {
        imu_data_.linear_acceleration_x = data.linear_acceleration_x;
        imu_data_.linear_acceleration_y = data.linear_acceleration_y;
        imu_data_.linear_acceleration_z = data.linear_acceleration_z;
        imu_data_.angular_velocity_x = data.angular_velocity_x;
        imu_data_.angular_velocity_y = data.angular_velocity_y;
        imu_data_.angular_velocity_z = angular_velocity_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;

#if 0
        if (imu_data_file_.is_open())
        {
            imu_data_file_ << data.system_timestamp << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << angular_velocity_z << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << angular_velocity_z << std::endl;
        }

#endif

        SetImuData(imu_data_);
    }

    {
        std::lock_guard<std::mutex> lock(raw_imu_mtx_);
        raw_imu_data_.angular_velocity_x = data.angular_velocity_x;
        raw_imu_data_.angular_velocity_y = data.angular_velocity_y;
        raw_imu_data_.angular_velocity_z = data.angular_velocity_z;
        raw_imu_data_.linear_acceleration_x = data.linear_acceleration_x;
        raw_imu_data_.linear_acceleration_y = data.linear_acceleration_y;
        raw_imu_data_.linear_acceleration_z = data.linear_acceleration_z;
        raw_imu_data_.frame_timestamp = data.frame_timestamp;
        raw_imu_data_.system_timestamp = data.system_timestamp;
        if (mower_alg_)
        {
            mower_alg_->SetRawImuData(raw_imu_data_);
        }
    }
}

void NavigationMowerNode::DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);

    if (enable_motor_speed_filter_)
    {
        filter_state_.motor_speed_left = LowPassFilter(data.motor_speed_left, filter_state_.motor_speed_left, alpha_speed_);
        filter_state_.motor_speed_right = LowPassFilter(data.motor_speed_right, filter_state_.motor_speed_right, alpha_speed_);
        motor_speed_data_.motor_speed_left = filter_state_.motor_speed_left;
        motor_speed_data_.motor_speed_right = filter_state_.motor_speed_right;
        motor_speed_data_.frame_timestamp = data.frame_timestamp;
        motor_speed_data_.system_timestamp = data.system_timestamp;
        // motor_speed_data_.current_left = data.motor_current_left; // 电流
        // motor_speed_data_.current_right = data.motor_current_right;

        SetMotorSpeedData(motor_speed_data_);
    }
    else
    {
        motor_speed_data_.motor_speed_left = data.motor_speed_left;
        motor_speed_data_.motor_speed_right = data.motor_speed_right;
        motor_speed_data_.frame_timestamp = data.frame_timestamp;
        motor_speed_data_.system_timestamp = data.system_timestamp;
        // motor_speed_data_.current_left = data.motor_current_left; // 电流
        // motor_speed_data_.current_right = data.motor_current_right;
        // update encoder datas

        SetMotorSpeedData(motor_speed_data_);
    }
}

void NavigationMowerNode::DealMotionDetectionResult(const fescue_msgs__msg__LocalizationMotionDetectionResult &data)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_.ave_pix_diff = data.ave_pix_diff;
    motion_detection_result_.is_motion = data.is_motion;
    motion_detection_result_.timestamp = data.timestamp_ms;
    // LOG_ERROR("[DealMotionDetectionResult] ave_pix_diff: {}, is_motion: {}", data.ave_pix_diff, data.is_motion);
    // LOG_ERROR("[DealMotionDetectionResult] timestamp: {}", data.timestamp_ms);

    SetMotionDetectionResult(motion_detection_result_);
}

void NavigationMowerNode::DealSlopeDetectionResult(const mower_msgs::msg::LawnmowerSlopeStatus &data)
{
    std::lock_guard<std::mutex> lock(slope_detection_result_mtx_);
    slope_detection_result_.roll = data.roll;
    slope_detection_result_.pitch = data.pitch;
    slope_detection_result_.yaw = data.yaw;
    slope_detection_result_.slope_status = static_cast<int>(data.slope_status);
    if (mower_alg_)
    {
        mower_alg_->SetSlopeDetectionResult(slope_detection_result_);
    }
}

void NavigationMowerNode::DealChargeDetectResult(const fescue_msgs__msg__ChargeResult &msg)
{
    std::lock_guard<std::mutex> lock(station_mtx_);
    charge_station_result_.timestamp_ms = msg.timestamp;
    charge_station_result_.is_chargestation = msg.is_charge;
    charge_station_result_.is_head = msg.is_head;
    charge_station_result_.range = msg.range;
    charge_station_result_.direction = static_cast<ChargeStationDirection>(msg.direction);
    charge_station_result_.pose = static_cast<ChargeStationPose>(msg.pose);
    if (msg.is_charge)
    {
        charge_station_result_.station_box.clear();
        for (size_t i = 0; i < IOX_MAX_STATION_BOX_SIZE; i++)
        {
            charge_station_result_.station_box.push_back(msg.station_box[i]);
        }
        charge_station_result_.charge_station_center_error = int((msg.station_box[2] + msg.station_box[4]) / 2) - 320;
    }
    if (msg.is_head)
    {
        charge_station_result_.head_box.clear();
        for (size_t i = 0; i < IOX_MAX_HEAD_BOX_SIZE; i++)
        {
            charge_station_result_.head_box.push_back(msg.head_box[i]);
        }
        charge_station_result_.head_center_error = int((msg.head_box[2] + msg.head_box[4]) / 2) - 320;
    }
}

void NavigationMowerNode::PrintNavAlgCtrlData(const fescue_msgs__msg__NavigationAlgoCtrlData &data)
{
    if (last_ctrl_data_ != data)
    {
        last_ctrl_data_ = data;
        LOG_DEBUG("Subscriber {} nav alg ctrl request, size: {}!", data.sender.c_str(), data.data.size());
        for (size_t i = 0; i < data.data.size(); i++)
        {
            LOG_DEBUG("Subscriber {} nav alg ctrl {} {}", data.sender.c_str(),
                      NavigationAlgoTypeToString(data.data[i].type), NavigationAlgoStateToString(data.data[i].state));
        }
    }
}

void NavigationMowerNode::DealOtherTaskNavAlgCtrl(const fescue_msgs__msg__NavigationAlgoCtrlData &data)
{
    DealFeatureSelectCallback(data);
}

void NavigationMowerNode::DealFunctionState(const fescue_msgs_FunctionStateData &msg)
{
    const auto &function_state = msg.state;
    if (mower_alg_)
    {
        mower_alg_->SetFunctionState(function_state);
    }
}

void NavigationMowerNode::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    if (mower_alg_)
    {
        mower_alg_->SetMotorSpeedData(motor_speed_data);
    }
}

void NavigationMowerNode::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    if (mower_alg_)
    {
        mower_alg_->SetMotionDetectionResult(motion_detection_result);
    }
}

void NavigationMowerNode::SetImuData(const ImuData &imu_data)
{
    if (mower_alg_)
    {
        mower_alg_->SetImuData(imu_data);
    }
}

float NavigationMowerNode::LowPassFilter(float new_value, float prev_value, float alpha)
{
    return alpha * new_value + (1.0f - alpha) * prev_value;
}

void NavigationMowerNode::DealMCUSensor(const mower_msgs::msg::McuSensor &data)
{
    LOG_DEBUG("charge_terminal_status is: {}", data.charge_terminal_status);
    if (mower_alg_)
    {
        mower_alg_->SetMCUSensor(data);
    }
}

void NavigationMowerNode::DealMowerTwist(const geometry_msgs__msg__Twist_iox &msg)
{
    std::string sender = std::string(msg.sender.c_str());
    float linear_velocity = msg.linear.x;
    float angular_velocity = msg.angular.z;

    PublishMowerTwist(sender, linear_velocity, angular_velocity);

    {
        std::lock_guard<std::mutex> lock(velocity_data_mtx_);
        velocity_data_.linear_velocity = msg.linear.x;
        velocity_data_.angular_velocity = msg.angular.z;
    }
}

void NavigationMowerNode::DealCrossRegionState(const fescue_msgs__msg__CrossRegionStateData &msg)
{
    std::lock_guard<std::mutex> lk(cross_region_state_mtx_);
    cross_region_state_.store(static_cast<CrossRegionRunningState>(msg.state));
    if (last_cross_region_state_.load() != cross_region_state_.load())
    {
        LOG_WARN("########## Recv cross region state, sender:{}, state:{}", msg.sender.c_str(), asStringLiteral(cross_region_state_.load()));
        last_cross_region_state_.store(cross_region_state_.load());
    }
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (cross_region_state == CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS)
    {
        LOG_WARN("######### Send cross region ENTER channel!");
        PublishBeaconStatus(mower_msgs::msg::BeaconStatusType::ENTER);
    }
    else if (cross_region_state == CrossRegionRunningState::FINISH ||
             cross_region_state == CrossRegionRunningState::UNDEFINED)
    {
        LOG_WARN("######### Send cross region EXIT channel!");
        PublishBeaconStatus(mower_msgs::msg::BeaconStatusType::EXIT);
    }
}

void NavigationMowerNode::DealRechargeState(const fescue_msgs__msg__RechargeStateData &msg)
{
    recharge_state_.store(static_cast<RechargeRunningState>(msg.state));
    RechargeRunningState recharge_state = recharge_state_.load();

    if (recharge_state == RechargeRunningState::ACCURATE_DOCK)
    {
        if (last_recharge_state_.load() != recharge_state)
        {
            LOG_WARN("Rrecharge have finded QR Code! last: {} current {}",
                     asStringLiteral(last_recharge_state_.load()), asStringLiteral(recharge_state));
            last_recharge_state_.store(recharge_state);
            // SetPerceptionAndLocalizationStateOnRechargeHaveFindQRCode();
            SendRechargeFinalResult(false, false, mower_msgs::srv::RechargeOperationStatus::NOT_INTERRUPTIBLE);
        }
    }
    else if (recharge_state == RechargeRunningState::RECHARGE_FINISH)
    {
        last_recharge_state_.store(recharge_state);
    }
    else
    {
        if (last_recharge_state_.load() != recharge_state)
        {
            LOG_WARN("Rrecharge on finding QR Code! last: {} current {}",
                     asStringLiteral(last_recharge_state_.load()), asStringLiteral(recharge_state));
            last_recharge_state_.store(recharge_state);
            // SetPerceptionAndLocalizationStateOnRechargeFindingQRCode();
            SendRechargeFinalResult(false, false, mower_msgs::srv::RechargeOperationStatus::INTERRUPTIBLE);
        }
    }
}

void NavigationMowerNode::DealBehaviorState(const fescue_msgs__msg__BehaviorStateData &msg)
{
    LOG_INFO("[DealBehaviorState] behavior_state:({}) is_loop: {} exception_types size: {}", static_cast<int>(msg.state), msg.is_loop, msg.exception_types.size());
    std::lock_guard<std::mutex> lck(behavior_mtx_);
    behavior_state_ = static_cast<BehaviorRunningState>(msg.state);
    is_behavior_loop_ = msg.is_loop;
    triggered_exception_types_.clear();
    for (size_t i = 0; i < msg.exception_types.size(); i++)
    {
        triggered_exception_types_.push_back(static_cast<BehaviorExceptionType>(msg.exception_types[i]));
    }

    last_behavior_timeout_time_ = std::chrono::steady_clock::now();
}

void NavigationMowerNode::DealQRCodeLocationResult(const fescue_msgs__msg__QrCodeResult &msg)
{
    // LOG_DEBUG("QRLocation: perception_status {} status {} pose({} {} {}) rpw({} {} {})",
    //           msg.mark_perception_status, msg.status,
    //           msg.pose.position.x, msg.pose.position.y, msg.pose.position.z,
    //           msg.roll, msg.pitch, msg.yaw);

    std::lock_guard<std::mutex> lock(qrcode_loc_mtx_);
    qrcode_loc_result_.timestamp_ms = msg.timestamp_ms;
    qrcode_loc_result_.mark_perception_status = msg.mark_perception_status;
    qrcode_loc_result_.mark_perception_direction = msg.mark_perception_direction;
    qrcode_loc_result_.detect_status = static_cast<QRCodeDetectStatus>(msg.status);
    for (size_t i = 0; i < msg.qrcode_dis.size(); i++)
    {
        std::pair<int, float> pair{msg.qrcode_dis[i].id, msg.qrcode_dis[i].distance};
        qrcode_loc_result_.v_markID_dis.push_back(pair);
    }
    qrcode_loc_result_.markID = msg.mark_id;
    qrcode_loc_result_.target_direction = msg.target_direction;
    qrcode_loc_result_.xyzrpw.x = msg.pose.position.x;
    qrcode_loc_result_.xyzrpw.y = msg.pose.position.y;
    qrcode_loc_result_.xyzrpw.z = msg.pose.position.z;
    qrcode_loc_result_.xyzrpw.r = msg.roll;
    qrcode_loc_result_.xyzrpw.p = msg.pitch;
    qrcode_loc_result_.xyzrpw.w = msg.yaw;
    if (mower_alg_)
    {
        mower_alg_->SetQRCodeLocationResult(qrcode_loc_result_);
    }
}

void NavigationMowerNode::DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg)
{
    std::lock_guard<std::mutex> lck(mark_loc_mtx_);
    mark_loc_result_.timestamp = msg.timestamp_ms;
    mark_loc_result_.mark_perception_status = msg.mark_perception_status;
    mark_loc_result_.mark_perception_direction = msg.mark_perception_direction;
    mark_loc_result_.detect_status = static_cast<int>(msg.detect_status);
    mark_loc_result_.roi_confidence = msg.roi_confidence;
    mark_loc_result_.target_direction = msg.target_direction;
    mark_loc_result_.mark_id = msg.mark_id;
    mark_loc_result_.xyzrpw.x = msg.pose.position.x;
    mark_loc_result_.xyzrpw.y = msg.pose.position.y;
    mark_loc_result_.xyzrpw.z = msg.pose.position.z;
    mark_loc_result_.xyzrpw.r = msg.roll;
    mark_loc_result_.xyzrpw.p = msg.pitch;
    mark_loc_result_.xyzrpw.w = msg.yaw;
    mark_loc_result_.mark_id_distance.clear();
    for (size_t i = 0; i < msg.mark_id_dis.size(); i++)
    {
        MarkIdDistance mark_id_dis;
        mark_id_dis.mark_id = msg.mark_id_dis[i].id;
        mark_id_dis.distance = msg.mark_id_dis[i].distance;
        mark_loc_result_.mark_id_distance.push_back(mark_id_dis);
    }
}

void NavigationMowerNode::DealRandomMowerState(const fescue_msgs__msg__RandomMowerStateData &msg)
{
    // if (mower_alg_)
    // {
    //     mower_alg_->SetRandomMowerRunningState(static_cast<RandomMowerRunningState>(msg.state));
    // }

    random_mower_state_.store(static_cast<RandomMowerRunningState>(msg.state));
    LOG_INFO_THROTTLE(2000, "Random mower state is: {}", asStringLiteral(random_mower_state_.load()));
}

// void NavigationMowerAlg::SetRandomMowerRunningState(RandomMowerRunningState state)
// {
//     if (state == RandomMowerRunningState::FINISH)
//     {
//         is_mowing_time_over_.store(true);
//     }
//     else
//     {
//         is_mowing_time_over_.store(false);
//     }
// }

void NavigationMowerNode::CheckMCUExceptionTimeout()
{
    std::lock_guard<std::mutex> lock(mcu_exception_mutex_);
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_mcu_exception_time_).count();
    if (duration > 100)
    {
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        LOG_WARN_THROTTLE(3000, "[MowerThread] MCUException data timeout {} ms, status restored to NORMAL", duration);
    }
}

void NavigationMowerNode::CheckBehaviorStateTimeout()
{
    std::lock_guard<std::mutex> lock(behavior_mtx_);
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_behavior_timeout_time_).count();
    if (duration > 100)
    {
        behavior_state_ = BehaviorRunningState::UNDEFINED;
        is_behavior_loop_ = false;
        triggered_exception_types_.clear();
        LOG_WARN_THROTTLE(3000, "[MowerThread] behavior_state_ data timeout over {} ms, status reset to UNDEFINED", duration);
    }
}

void NavigationMowerNode::CheckTimeouts()
{
    CheckMCUExceptionTimeout();
    CheckBehaviorStateTimeout();
}

void NavigationMowerNode::MowerThread()
{
    MowerAlgResult mower_result;
    MarkLocationResult mark_loc_result;
    QRCodeLocationResult qrcode_loc_result;
    CrossRegionRunningState cross_region_state;
    PerceptionFusionResult fusion_result;
    RechargeRunningState recharge_state;
    McuExceptionStatus mcu_exception_status;
    BehaviorRunningState behavior_state;
    bool is_behavior_loop;
    std::vector<BehaviorExceptionType> triggered_exception_types;
    RandomMowerRunningState random_mower_state;
    ChargeStationDetectResult station_result;

    while (thread_running_.load())
    {
        if (mower_enable_.load())
        {
            CheckTimeouts();
            cross_region_state = cross_region_state_.load();
            recharge_state = recharge_state_.load();
            random_mower_state = random_mower_state_.load();
            bool is_new_fusion = false;
            {
                std::scoped_lock lock(fusion_mutex_, mark_loc_mtx_, qrcode_loc_mtx_, mcu_exception_mutex_, behavior_mtx_, station_mtx_);
                fusion_result = fusion_result_;
                mark_loc_result = mark_loc_result_;
                qrcode_loc_result = qrcode_loc_result_;
                mcu_exception_status = mcu_exception_status_;
                behavior_state = behavior_state_;
                is_behavior_loop = is_behavior_loop_;
                triggered_exception_types = triggered_exception_types_;
                station_result = charge_station_result_;
                is_new_fusion = is_new_fusion_.load();
            }
            if (mower_alg_)
            {
                mower_result = mower_alg_->Run(mark_loc_result, cross_region_state, qrcode_loc_result,
                                               fusion_result, is_new_fusion, recharge_state, mcu_exception_status,
                                               behavior_state, is_behavior_loop, triggered_exception_types,
                                               random_mower_state, station_result);
                if (mower_result.mower_completed)
                {
                    LOG_INFO("NavigationMowerAlg completed!");
                    mower_alg_->ResetMowerAlgFlags();
                }
                // publish Fusion pose
                PublishFusionPose();
                PublishDangerousPointCloud();
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationMowerAlg disable!");
            if (mower_alg_)
            {
                mower_alg_->ResetMowerAlgFlags();
            }
        }

        if (save_record_data_.load())
        {
            RecordData();
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void NavigationMowerNode::RecordData()
{
    if (mower_alg_ == nullptr)
    {
        return;
    }
    {
        std::lock_guard<std::mutex> lock(data_file_mutex_);
        if (!data_file_.is_open())
        {
            return;
        }
        // Close the file and create a new one after timeout
        uint64_t timestamp_ms = GetSteadyClockTimestampMs();
        uint64_t create_file_interval = 10 * 60 * 1000;
        if (timestamp_ms - last_create_file_time_ > create_file_interval)
        {
            if (data_file_.is_open())
            {
                LOG_INFO("Time interval reached, closing data file and creating a new one");
                data_file_.close();
                auto time_str = GetCurrentTimeString();
                std::string data_file_path = "/userdata/log/test_data_" + time_str + ".txt";
                data_file_.open(data_file_path);
                last_create_file_time_ = timestamp_ms;
                is_imu_calibration_recorded_ = false;
            }
        }
    }
    // Record IMU calibration parameters
    if (!is_imu_calibration_recorded_ && mower_alg_->IsImuCalibrationValid())
    {
        is_imu_calibration_recorded_ = true;
        nlohmann::json imu_calibration_data;
        const auto &imu_calibration_config = mower_alg_->GetImuCalibrationConfig();
        imu_calibration_data["qx"] = imu_calibration_config.qx;
        imu_calibration_data["qy"] = imu_calibration_config.qy;
        imu_calibration_data["qz"] = imu_calibration_config.qz;
        imu_calibration_data["qw"] = imu_calibration_config.qw;
        imu_calibration_data["acc_bias_x"] = imu_calibration_config.acc_bias_x;
        imu_calibration_data["acc_bias_y"] = imu_calibration_config.acc_bias_y;
        imu_calibration_data["acc_bias_z"] = imu_calibration_config.acc_bias_z;
        imu_calibration_data["gyro_bias_x"] = imu_calibration_config.gyro_bias_x;
        imu_calibration_data["gyro_bias_y"] = imu_calibration_config.gyro_bias_y;
        imu_calibration_data["gyro_bias_z"] = imu_calibration_config.gyro_bias_z;
        auto imu_calibration_data_str = imu_calibration_data.dump(-1);
        buffer_data_.push_back(imu_calibration_data_str);
    }
    const auto &fusion_data = mower_alg_->GetFusionData();
    nlohmann::json data;
    // 记录系统时间字符串
    std::string system_time_str = GetCurrentTimeString();
    data["aa_system_time"] = system_time_str;
    data["timestamp"] = fusion_data.time;
    {
        data["imu"]["frame_timestamp"] = fusion_data.imu_data.frame_timestamp;
        data["imu"]["system_timestamp"] = fusion_data.imu_data.system_timestamp;
        data["imu"]["linear_acceleration_x"] = fusion_data.imu_data.linear_acceleration_x;
        data["imu"]["linear_acceleration_y"] = fusion_data.imu_data.linear_acceleration_y;
        data["imu"]["linear_acceleration_z"] = fusion_data.imu_data.linear_acceleration_z;
        data["imu"]["angular_velocity_x"] = fusion_data.imu_data.angular_velocity_x;
        data["imu"]["angular_velocity_y"] = fusion_data.imu_data.angular_velocity_y;
        data["imu"]["angular_velocity_z"] = fusion_data.imu_data.angular_velocity_z;
    }
    {
        data["motor_speed"]["frame_timestamp"] = fusion_data.motor_speed_data.frame_timestamp;
        data["motor_speed"]["system_timestamp"] = fusion_data.motor_speed_data.system_timestamp;
        data["motor_speed"]["motor_speed_left"] = fusion_data.motor_speed_data.motor_speed_left;
        data["motor_speed"]["motor_speed_right"] = fusion_data.motor_speed_data.motor_speed_right;
        data["motor_speed"]["current_left"] = fusion_data.motor_speed_data.current_left;
        data["motor_speed"]["current_right"] = fusion_data.motor_speed_data.current_right;
    }
    {
        data["motion_detection_result"]["ave_pix_diff"] = fusion_data.motion_detection_result.ave_pix_diff;
        data["motion_detection_result"]["timestamp"] = fusion_data.motion_detection_result.timestamp;
        data["motion_detection_result"]["is_motion"] = fusion_data.motion_detection_result.is_motion;
    }
    {
        data["slope_result"]["roll"] = fusion_data.slope_detection_result.roll;
        data["slope_result"]["pitch"] = fusion_data.slope_detection_result.pitch;
        data["slope_result"]["yaw"] = fusion_data.slope_detection_result.yaw;
        data["slope_result"]["slope_status"] = (int)fusion_data.slope_detection_result.slope_status;
    }
    {
        // fusion pose不用加锁，因为在一个线程中
        data["fusion_pose"]["x"] = fusion_pose_.x;
        data["fusion_pose"]["y"] = fusion_pose_.y;
        data["fusion_pose"]["yaw"] = fusion_pose_.yaw;
        data["fusion_pose"]["pitch"] = fusion_pose_.pitch;
        data["fusion_pose"]["roll"] = fusion_pose_.roll;
    }
    {
        std::lock_guard<std::mutex> lock(velocity_data_mtx_);
        data["velocity_data"]["linear_velocity"] = velocity_data_.linear_velocity;
        data["velocity_data"]["angular_velocity"] = velocity_data_.angular_velocity;
    }
    {
        data["acceleration_filter_data"]["ax_filter_val"] = fusion_data.acceleration_filter_data.ax_filter_val;
        data["acceleration_filter_data"]["ay_filter_val"] = fusion_data.acceleration_filter_data.ay_filter_val;
        data["acceleration_filter_data"]["ax_window"] = nlohmann::json::array();
        data["acceleration_filter_data"]["ay_window"] = nlohmann::json::array();
        for (const auto &val : fusion_data.acceleration_filter_data.ax_window)
        {
            data["acceleration_filter_data"]["ax_window"].push_back(val);
        }
        for (const auto &val : fusion_data.acceleration_filter_data.ay_window)
        {
            data["acceleration_filter_data"]["ay_window"].push_back(val);
        }
    }
    if (is_new_fusion_result_record_.load())
    {
        uint64_t output_timestamp;
        is_new_fusion_result_record_ = false;
        OccupancyResult occupancy_result;
        {
            std::lock_guard<std::mutex> lock(fusion_mutex_);
            occupancy_result = fusion_result_.occupancy_grid;
            output_timestamp = fusion_result_.output_timestamp;
        }
        data["fusion_result"]["output_timestamp"] = output_timestamp;
        data["occupancy_result"]["width"] = occupancy_result.width;
        data["occupancy_result"]["height"] = occupancy_result.height;
        data["occupancy_result"]["resolution"] = occupancy_result.resolution;
        data["occupancy_result"]["grid"] = nlohmann::json::array();
        for (int i = 0; i < occupancy_result.height; i++)
        {
            for (int j = 0; j < occupancy_result.width; j++)
            {
                data["occupancy_result"]["grid"].push_back(occupancy_result.grid[i][j]);
            }
        }
    }

    if (dangerous_point_cloud_.is_new)
    {
        data["dangerous_point_cloud"]["is_new"] = dangerous_point_cloud_.is_new;
        data["dangerous_point_cloud"]["pose"]["x"] = dangerous_point_cloud_.pose.x;
        data["dangerous_point_cloud"]["pose"]["y"] = dangerous_point_cloud_.pose.y;
        data["dangerous_point_cloud"]["pose"]["theta"] = dangerous_point_cloud_.pose.theta;
        data["dangerous_point_cloud"]["point_cloud"]["x"] = nlohmann::json::array();
        data["dangerous_point_cloud"]["point_cloud"]["y"] = nlohmann::json::array();
        for (const auto &point : dangerous_point_cloud_.point_cloud)
        {
            data["dangerous_point_cloud"]["point_cloud"]["x"].push_back(point.x);
            data["dangerous_point_cloud"]["point_cloud"]["y"].push_back(point.y);
        }
    }

    auto data_str = data.dump(-1);
    buffer_data_.push_back(data_str);
    if (buffer_data_.size() >= 20)
    {
        std::lock_guard<std::mutex> lock(data_file_mutex_);
        if (data_file_.is_open())
        {
            for (const auto &data_str : buffer_data_)
            {
                data_file_ << data_str << "\n";
            }
        }
        buffer_data_.clear();
    }
}

void NavigationMowerNode::PublishDangerousPointCloud()
{
    if (mower_alg_ == nullptr || pub_nav_dangerous_point_cloud_ == nullptr)
    {
        return;
    }
    auto dangerous_point_cloud = mower_alg_->GetDangerousPointCloud();
    dangerous_point_cloud_ = dangerous_point_cloud;
    if (!dangerous_point_cloud.is_new)
    {
        return;
    }
    fescue_iox::ob_mower_msgs::NavPointCloud nav_point_cloud;
    uint64_t timestamp_ms = GetSteadyClockTimestampMs();
    nav_point_cloud.header.stamp.sec = timestamp_ms / 1000;
    nav_point_cloud.header.stamp.nanosec = (timestamp_ms % 1000) * 1000000;
    nav_point_cloud.cur_pose_x = dangerous_point_cloud.pose.x;
    nav_point_cloud.cur_pose_y = dangerous_point_cloud.pose.y;
    nav_point_cloud.cur_pose_yaw = dangerous_point_cloud.pose.theta;
    for (const auto &point : dangerous_point_cloud.point_cloud)
    {
        fescue_iox::ob_mower_msgs::PointXy point_xy;
        point_xy.x = point.x;
        point_xy.y = point.y;
        nav_point_cloud.point_cloud.push_back(point_xy);
    }
    pub_nav_dangerous_point_cloud_->publish(nav_point_cloud);
}

void NavigationMowerNode::DealDataFile(const MowerRunningState &state)
{
    auto time_str = GetCurrentTimeString();
    // 拼接时间字符串到文件名上
    std::string data_file_path = "/userdata/log/test_data_" + time_str + ".txt";
    std::lock_guard<std::mutex> lock(data_file_mutex_);

    if (state == MowerRunningState::RUNNING)
    {
        if (!data_file_.is_open())
        {
            LOG_INFO("open data file path");
            data_file_.open(data_file_path);
            last_create_file_time_ = GetSteadyClockTimestampMs();
            is_imu_calibration_recorded_ = false;
        }
    }
    else
    {
        if (data_file_.is_open())
        {
            LOG_INFO("close data file path");
            data_file_.close();
        }
    }
}

bool NavigationMowerNode::ConvertFeatureSelectToNavAlgCtrlData(const std::vector<FeatureSelectData> &data, fescue_msgs__msg__NavigationAlgoCtrlData &ctrl_data)
{
    if (data.empty())
    {
        return false;
    }
    size_t data_size = data.size() > MAX_NAVIGATION_ALGO_NUM ? MAX_NAVIGATION_ALGO_NUM : data.size();
    ctrl_data.sender.unsafe_assign("NavigationMowerNode");
    for (size_t i = 0; i < data_size; i++)
    {
        fescue_msgs__msg__NavigationAlgoCtrlInfo info;
        info.type = static_cast<fescue_msgs__enum__NavigationAlgoType>(data.at(i).alg_id);
        info.state = static_cast<fescue_msgs__enum__NavigationAlgoState>(data.at(i).alg_status);
        for (const auto &exception_type : data.at(i).behavior_exception_types)
        {
            info.behavior_exception_types.push_back(static_cast<fescue_msgs__enum__BehaviorExceptionType>(exception_type));
        }
        info.is_recover_from_exception = data.at(i).is_recover_from_exception;
        info.is_behavior_loop = data.at(i).is_behavior_loop;
        for (const auto &exception_type : data.at(i).last_triggered_exception_types)
        {
            info.last_triggered_exception_types.push_back(static_cast<fescue_msgs__enum__BehaviorExceptionType>(exception_type));
        }
        ctrl_data.data.push_back(info);
    }
    return true;
}

void NavigationMowerNode::DealFeatureSelectCallback(const fescue_msgs__msg__NavigationAlgoCtrlData &data)
{
    if (pub_nav_alg_ctrl_)
    {
        std::lock_guard<std::mutex> lock(nav_ctrl_mutex_);
        PrintNavAlgCtrlData(data);
        pub_nav_alg_ctrl_->publish(data);
    }
}

bool NavigationMowerNode::DealMarkLocationMarkIdCallback(int mark_id)
{
    auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetDetectMarkId_Request,
                                                      fescue_msgs__srv__SetDetectMarkId_Response>>("mark_location_set_detect_mark_id");
    fescue_msgs__srv__SetDetectMarkId_Request request_input;
    fescue_msgs__srv__SetDetectMarkId_Response response_output;
    request_input.data.mark_id = mark_id;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    return response_output.success;
}

bool NavigationMowerNode::SendCrossRegionFinalResult(bool cross_region_result)
{
    LOG_INFO("Send cross region final result!");
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CrossRegionResultRequest,
                                                      mower_msgs::srv::CrossRegionResultResponse>>("cross_region_result");
    mower_msgs::srv::CrossRegionResultRequest request_input;
    mower_msgs::srv::CrossRegionResultResponse response_output;
    request_input.cross_region_result_type = cross_region_result ? mower_msgs::srv::CrossRegionResultType::SUCCESS
                                                                 : mower_msgs::srv::CrossRegionResultType::FAIL;
    if (client->SendRequest(request_input, response_output))
    {
        return false;
    }
    return response_output.success;
}

bool NavigationMowerNode::SendRechargeFinalResult(bool recharge_completed, bool recharge_result, mower_msgs::srv::RechargeOperationStatus status)
{
    LOG_INFO("Send recharge final result!");
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::RechargeResultRequest,
                                                      mower_msgs::srv::RechargeResultResponse>>("recharge_result");
    mower_msgs::srv::RechargeResultRequest request_input;
    mower_msgs::srv::RechargeResultResponse response_output;
    request_input.timestamp_ms = GetSteadyClockTimestampMs();
    request_input.completed = recharge_completed;
    request_input.result = recharge_result;
    request_input.operation_status = status;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    return response_output.success;
}

bool NavigationMowerNode::SendUndockFinalResult(bool undock_completed, bool undock_result, mower_msgs::srv::UndockOperationStatus status)
{
    LOG_INFO("Send undock final result!");
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::UndockResultRequest,
                                                      mower_msgs::srv::UndockResultResponse>>("undock_result");
    mower_msgs::srv::UndockResultRequest request_input;
    mower_msgs::srv::UndockResultResponse response_output;
    request_input.timestamp_ms = GetSteadyClockTimestampMs();
    request_input.completed = undock_completed;
    request_input.result = undock_result;
    request_input.operation_status = status;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    return response_output.success;
}

bool NavigationMowerNode::SendSpiralMowerFinalResult(bool spiral_completed, bool spiral_result)
{
    LOG_INFO("Send spiral mower final result!");
    (void)spiral_completed;
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::SpiralMowResultRequest,
                                                      mower_msgs::srv::SpiralMowResultResponse>>("spiral_mow_result");
    mower_msgs::srv::SpiralMowResultRequest request_input;
    mower_msgs::srv::SpiralMowResultResponse response_output;
    request_input.timestamp_ms = GetSteadyClockTimestampMs();
    request_input.result = spiral_result;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    return response_output.success;
}

bool NavigationMowerNode::SendRegionExporeFinalResult(const RegionExploreResult &result)
{
    LOG_INFO("Send region expore final result!");
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::ExploreMapResultRequest,
                                                      mower_msgs::srv::ExploreMapResultResponse>>("explore_map_result");
    mower_msgs::srv::ExploreMapResultRequest request_input;
    mower_msgs::srv::ExploreMapResultResponse response_output;
    request_input.timestamp = result.timestamp;
    request_input.result = result.result;
    request_input.master_region_map_result = result.master_region_map_result;
    request_input.slave_region_map_result = result.slave_region_map_result;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    return response_output.success;
}

bool NavigationMowerNode::SendCutBorderFinalResult(bool result)
{
    LOG_INFO("Send cut border final result!");
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CutEdgeResultRequest,
                                                      mower_msgs::srv::CutEdgeResultResponse>>("cut_edge_result");
    mower_msgs::srv::CutEdgeResultRequest request_input;
    mower_msgs::srv::CutEdgeResultResponse response_output;
    request_input.timestamp = GetSteadyClockTimestampMs();
    request_input.result = result;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    return response_output.success;
}

bool NavigationMowerNode::DealAreaCalcStart(uint64_t timestamp_ms)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::AreaCalculationStartRequest,
                                                      mower_msgs::srv::AreaCalculationStartResponse>>("localization_area_calculation_start");
    mower_msgs::srv::AreaCalculationStartRequest request_input;
    mower_msgs::srv::AreaCalculationStartResponse response_output;
    request_input.timestamp = timestamp_ms;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    return response_output.success;
}

bool NavigationMowerNode::DealAreaCalcStop(uint64_t timestamp_ms, float &area, float &perimeter)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::AreaCalculationStopRequest,
                                                      mower_msgs::srv::AreaCalculationStopResponse>>("localization_area_calculation_stop");
    mower_msgs::srv::AreaCalculationStopRequest request_input;
    mower_msgs::srv::AreaCalculationStopResponse response_output;
    request_input.timestamp = timestamp_ms;
    if (!client->SendRequest(request_input, response_output))
    {
        return false;
    }
    area = response_output.result.area;
    perimeter = response_output.result.perimeter;
    LOG_INFO("send area calc stop request success, area {:.2f} m^2, perimeter {:.2f} m", area, perimeter);
    return response_output.success;
}

void NavigationMowerNode::DealUndockFinalResult(bool completed, bool result, mower_msgs::srv::UndockOperationStatus status)
{
    LOG_WARN("NavigationMowerNode undock status: {}, completed: {}, result: {}", static_cast<int>(status), completed, result);
    SendUndockFinalResult(completed, result, status);
    if (completed)
    {
        SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE);
    }
}

bool NavigationMowerNode::DealRegionExploreAreaCalcStart(uint64_t timestamp)
{
    LOG_WARN("NavigationMowerNode region explore area calc start, timestamp: {}", timestamp);
    return DealAreaCalcStart(timestamp);
}

bool NavigationMowerNode::DealRegionExploreAreaCalcStop(uint64_t timestamp, float &area, float &perimeter)
{
    LOG_WARN("NavigationMowerNode region explore area calc stop, timestamp: {}", timestamp);
    return DealAreaCalcStop(timestamp, area, perimeter);
}

void NavigationMowerNode::DealRegionExploreResult(RegionExploreResult &result)
{
    LOG_WARN("NavigationMowerNode region explore completed, result: {}", result.result);
    SetMowerAppTriggersRegionExplore(false);
    SendRegionExporeFinalResult(result);
}

void NavigationMowerNode::DealCutBorderResult(bool completed, bool result)
{
    (void)completed;
    LOG_WARN("NavigationMowerNode cut border completed, result: {}", result);
    SetMowerAppTriggersCutBorder(false);
    SendCutBorderFinalResult(result);
}

void NavigationMowerNode::DealCrossRegionRunningStateCallback(CrossRegionRunningState state)
{
    if (pub_cross_region_state_mower_)
    {
        fescue_msgs__msg__CrossRegionStateData data;
        data.sender.unsafe_assign("NavigationMowerNode");
        data.state = static_cast<int>(state);
        pub_cross_region_state_mower_->publish(data);
    }
}

void NavigationMowerNode::DealEdgeFollowStatusCallback(int status)
{
    if (pub_edge_follow_running_status_)
    {
        std_msgs__msg__Int32_iox msg;
        msg.data = status;
        pub_edge_follow_running_status_->publish(msg);
    }
}

bool NavigationMowerNode::DealSWStandByRequest(const mower_msgs::srv::GoToStandbyRequestType &data)
{
    if (data == mower_msgs::srv::GoToStandbyRequestType::STANDBY)
    {
        LOG_WARN("Receive SW STANDBY request, stop all task!");
        is_recharge_start_ = false;
        is_cross_region_start_ = false;
        is_random_mower_start_ = false;
        is_region_explore_start_ = false;
        is_spiral_mower_start_ = false;
        is_cut_border_start_ = false;
        CloseAllTask();
        SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::DISABLE);
    }

    return true;
}

bool NavigationMowerNode::DealSWMowerRequest(const mower_msgs::srv::GoMowRequestType &data)
{
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (is_cross_region_start_)
    {
        if (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
            cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
            cross_region_state == CrossRegionRunningState::UNDEFINED ||
            cross_region_state == CrossRegionRunningState::FINISH)
        {
            LOG_WARN("Receive SW RANDOM_MOW request, cross region state {}, stop it!", asStringLiteral(cross_region_state));
            is_cross_region_start_ = false;
            SetMowerAppTriggersCrossRegion(false);
        }
        else
        {
            LOG_WARN("Receive SW RANDOM_MOW request, cross region state {}, ignore it!", asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    RechargeRunningState recharge_state = recharge_state_.load();
    if (is_recharge_start_)
    {
        if ((recharge_state != RechargeRunningState::ACCURATE_DOCK) &&
            (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
             cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
             cross_region_state == CrossRegionRunningState::UNDEFINED ||
             cross_region_state == CrossRegionRunningState::FINISH))
        {
            LOG_WARN("Receive SW RANDOM_MOW request, recharge state {} cross region state {}, stop it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(false);
        }
        else
        {
            LOG_WARN("Receive SW RANDOM_MOW request, recharge state {} cross region state {}, ignore it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::GoMowRequestType::RANDOM_MOW)
    {
        LOG_WARN("Receive SW RANDOM_MOW request, run RANDOM_MOW!");
        SetPerceptionAndLocalizationStateOnRandomMower();
        SetMowerAppTriggersMower(true);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoMowRequestType::SPIRAL_MOW)
    {
        LOG_WARN("Receive SW SPIRAL_MOW request, run SPIRAL_MOW!");
        SetPerceptionAndLocalizationStateOnSpiralMower();
        SetMowerAppTriggersSpiralMower(true);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoMowRequestType::PAUSE)
    {
        LOG_WARN("Receive SW MOWER PAUSE request, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersMower(false);
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        LOG_ERROR("DealSWMowerRequest, Invalid request type {}!", static_cast<int>(data));
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
        return false;
    }

    return true;
}

bool NavigationMowerNode::DealSWChargeRequest(const mower_msgs::srv::GoChargeRequestType &data)
{
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (is_cross_region_start_)
    {
        if (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
            cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
            cross_region_state == CrossRegionRunningState::UNDEFINED ||
            cross_region_state == CrossRegionRunningState::FINISH)
        {
            LOG_WARN("Receive SW GO_CHARGE request, cross region state {}, stop it!", asStringLiteral(cross_region_state));
            is_cross_region_start_ = false;
            SetMowerAppTriggersCrossRegion(false);
        }
        else
        {
            LOG_WARN("Receive SW GO_CHARGE request, cross region state {}, ignore it!", asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::GoChargeRequestType::GO_TO_CHARGE)
    {
        LOG_WARN("Receive SW GO_TO_CHARGE request, run go charge!");
        SetPerceptionAndLocalizationStateOnRecharge();
        SetMowerAppTriggersRecharge(true);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoChargeRequestType::PAUSE)
    {
        LOG_WARN("Receive SW GO_TO_CHARGE PAUSE request, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        LOG_ERROR("DealSWChargeRequest, Invalid request type {}!", static_cast<int>(data));
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
        return false;
    }

    return true;
}

bool NavigationMowerNode::DealSWGoCrossRegionRequest(const mower_msgs::srv::GoToCrossRegionRequestType &data)
{
    RechargeRunningState recharge_state = recharge_state_.load();
    if (is_recharge_start_)
    {
        if (recharge_state == RechargeRunningState::ACCURATE_DOCK)
        {
            LOG_WARN("Receive SW CROS_REGION request, recharge state {}, ignore it!", asStringLiteral(recharge_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
        else
        {
            LOG_WARN("Receive SW CROS_REGION request, recharge state {}, stop it!", asStringLiteral(recharge_state));
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(false);
        }
    }

    if (data == mower_msgs::srv::GoToCrossRegionRequestType::GO_TO_CROSS_REGION)
    {
        LOG_WARN("Receive SW GO_TO_CROSS_REGION request, run cross region!");
        SetPerceptionAndLocalizationStateOnCrossRegion();
        SetMowerAppTriggersCrossRegion(true);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoToCrossRegionRequestType::PAUSE)
    {
        LOG_WARN("Receive SW CROSS_REGION PAUSE request, cross region running, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        LOG_ERROR("DealSWGoCrossRegionRequest, Invalid request type {}!", static_cast<int>(data));
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
        return false;
    }

    return true;
}

bool NavigationMowerNode::DealSWRegionExploreRequest(const mower_msgs::srv::ExploreMapRequestType &data)
{
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (is_cross_region_start_)
    {
        if (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
            cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
            cross_region_state == CrossRegionRunningState::UNDEFINED ||
            cross_region_state == CrossRegionRunningState::FINISH)
        {
            LOG_WARN("Receive SW REGION_EXPLORE request, cross region state {}, stop it!", asStringLiteral(cross_region_state));
            is_cross_region_start_ = false;
            SetMowerAppTriggersCrossRegion(false);
        }
        else
        {
            LOG_WARN("Receive SW REGION_EXPLORE request, cross region state {}, ignore it!", asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    RechargeRunningState recharge_state = recharge_state_.load();
    if (is_recharge_start_)
    {
        if ((recharge_state != RechargeRunningState::ACCURATE_DOCK) &&
            (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
             cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
             cross_region_state == CrossRegionRunningState::UNDEFINED ||
             cross_region_state == CrossRegionRunningState::FINISH))
        {
            LOG_WARN("Receive SW REGION_EXPLORE request, recharge state {} cross region state {}, stop it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(false);
        }
        else
        {
            LOG_WARN("Receive SW REGION_EXPLORE request, recharge state {} cross region state {}, ignore it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::ExploreMapRequestType::EXPLORE_MAP)
    {
        LOG_WARN("Receive SW EXPLORE_MAP request, run explore map!");
        SetPerceptionAndLocalizationStateOnExploreMap();
        SetMowerAppTriggersRegionExplore(true);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCutBorder(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::ExploreMapRequestType::PAUSE)
    {
        LOG_WARN("Receive SW EXPLORE_MAP PAUSE request, explore map running, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        LOG_ERROR("DealSWRegionExploreRequest, Invalid request type {}!", static_cast<int>(data));
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
        return false;
    }

    return true;
}

bool NavigationMowerNode::DealSWCutBorderRequest(const mower_msgs::srv::GoToCutEdgeRequestType &data)
{
    CrossRegionRunningState cross_region_state = cross_region_state_.load();
    if (is_cross_region_start_)
    {
        if (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
            cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
            cross_region_state == CrossRegionRunningState::UNDEFINED ||
            cross_region_state == CrossRegionRunningState::FINISH)
        {
            LOG_WARN("Receive SW CUT_BORDER request, cross region state {}, stop it!", asStringLiteral(cross_region_state));
            is_cross_region_start_ = false;
            SetMowerAppTriggersCrossRegion(false);
        }
        else
        {
            LOG_WARN("Receive SW CUT_BORDER request, cross region state {}, ignore it!", asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    RechargeRunningState recharge_state = recharge_state_.load();
    if (is_recharge_start_)
    {
        if ((recharge_state != RechargeRunningState::ACCURATE_DOCK) &&
            (cross_region_state == CrossRegionRunningState::EDGE_FINDING_BEACON ||
             cross_region_state == CrossRegionRunningState::PER_FOUND_BEACON ||
             cross_region_state == CrossRegionRunningState::UNDEFINED ||
             cross_region_state == CrossRegionRunningState::FINISH))
        {
            LOG_WARN("Receive SW CUT_BORDER request, recharge state {} cross region state {}, stop it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            is_recharge_start_ = false;
            SetMowerAppTriggersRecharge(false);
            SetMowerComplete(false);
        }
        else
        {
            LOG_WARN("Receive SW CUT_BORDER request, recharge state {} cross region state {}, ignore it!",
                     asStringLiteral(recharge_state), asStringLiteral(cross_region_state));
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                             mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
            return false;
        }
    }

    if (data == mower_msgs::srv::GoToCutEdgeRequestType::GO_TO_CUT_EDGE)
    {
        LOG_WARN("Receive SW GO_TO_CUT_EDGE request, run cut edge!");
        SetPerceptionAndLocalizationStateOnCutBorder();
        SetMowerAppTriggersCutBorder(true);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRecharge(false);
        mower_state_ = MowerRunningState::RUNNING;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else if (data == mower_msgs::srv::GoToCutEdgeRequestType::PAUSE)
    {
        LOG_WARN("Receive SW CUT_BORDER PAUSE request, cut edge running, PAUSE it!");
        mower_state_ = MowerRunningState::PAUSE;
        SetMowerRunningState(mower_state_);
        PublishRunningState(mower_state_);
    }
    else
    {
        LOG_ERROR("DealSWCutBorderRequest, Invalid request type {}!", static_cast<int>(data));
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_PNC_SW_COMMAND_EXECUTE_EXCEPTION);
        return false;
    }

    return true;
}

bool NavigationMowerNode::SetMowerNodeParam(const ob_mower_srvs::NavMowerNodeParamData &data)
{
    save_record_data_.store(data.save_record_data);
    save_imu_data_.store(data.save_imu_data);
    LOG_INFO("save record data is: {}, save imu data is: {}", save_record_data_.load(), save_imu_data_.load());
    if (save_imu_data_.load())
    {
        if (!imu_data_file_.is_open())
        {
            OpenImuDataFile();
        }
    }
    else
    {
        CloseImuDataFile();
    }
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();

    NavigationMowerNodeConfig config = Config<NavigationMowerNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<NavigationMowerNodeConfig>::SetConfig(config);
    LOG_INFO("New NavigationMowerNode params: {}", config.toString().c_str());
    return true;
}

bool NavigationMowerNode::GetMowerNodeParam(ob_mower_srvs::NavMowerNodeParamData &data)
{
    data.console_log_level.unsafe_assign(console_log_level_.c_str());
    data.file_log_level.unsafe_assign(file_log_level_.c_str());
    data.save_record_data = save_record_data_.load();
    data.save_imu_data = save_imu_data_.load();
    return true;
}

void NavigationMowerNode::VelociyAdjustment(float motor_speed_left, float motor_speed_right,
                                            float expected_linear, float expected_angular,
                                            float &actual_linear, float &actual_angular,
                                            float &adjusted_linear, float &adjusted_angular)
{
    // Calculate actual linear and angular velocity
    GetVelocityFromMotorRPM(motor_speed_left, motor_speed_right, wheel_radius_, wheel_base_, actual_linear, actual_angular);

    float adjustment_linear = adjusted_vel_kp_ * (expected_linear - actual_linear);
    float adjustment_angular = adjusted_vel_kp_ * (expected_angular - actual_angular);
    adjusted_linear = expected_linear + adjustment_linear;
    adjusted_angular = expected_angular + adjustment_angular;

    // Add limits for linear and angular velocity
    if ((adjusted_linear >= 0 && adjusted_linear > vel_limit_param_ * expected_linear) ||
        (adjusted_linear < 0 && adjusted_linear < vel_limit_param_ * expected_linear))
    {
        adjusted_linear = vel_limit_param_ * expected_linear;
    }
    if ((adjusted_angular >= 0 && adjusted_angular > vel_limit_param_ * expected_angular) ||
        (adjusted_angular < 0 && adjusted_angular < vel_limit_param_ * expected_angular))
    {
        adjusted_angular = vel_limit_param_ * expected_angular;
    }
}

void NavigationMowerNode::PublishMowerTwist(const std::string &sender, float linear, float angular)
{
    mower_msgs::msg::Twist twist;
    if (mower_state_ != MowerRunningState::RUNNING)
    {
        twist.linear_velocity = 0.0;
        twist.angular_velocity = 0.0;
    }
    else
    {
        if (slope_ctrl_enable_ && slope_ctrl_alg_)
        {
            SlopeDetectionResult slope_detection_result;
            {
                std::lock_guard<std::mutex> lock(slope_detection_result_mtx_);
                slope_detection_result = slope_detection_result_;
            }
            SlopeControlVelocityData input_vel{linear, angular};
            auto slope_ctrl_result = slope_ctrl_alg_->DoSlopeControl(input_vel, input_vel, slope_detection_result);
            LOG_WARN_THROTTLE(1000, "NavigationMowerNode velocity expected:({:.2f} {:.2f}), slope_ctrl:({:.2f} {:.2f})",
                              linear, angular, slope_ctrl_result.linear, slope_ctrl_result.angular);
            twist.linear_velocity = slope_ctrl_result.linear;
            twist.angular_velocity = slope_ctrl_result.angular;
        }
        else
        {
            twist.linear_velocity = linear;
            twist.angular_velocity = angular;
        }
    }
    LOG_WARN_THROTTLE(1000, "NavigationMowerNode publish [{}] MowerRunningState {} twist {:.2f} {:.2f}",
                      sender.c_str(), asStringLiteral(mower_state_), twist.linear_velocity, twist.angular_velocity);
    PublishMCUTwist(twist);
}

void NavigationMowerNode::PublishRunningState(MowerRunningState state)
{
    if (pub_nav_running_state_)
    {
        std::lock_guard<std::mutex> lk(nav_running_state_lock_);
        fescue_msgs__msg__NavigationRunningStateData running_state;
        running_state.sender.unsafe_assign("NavigationMowerNode");
        running_state.state = static_cast<int>(state);
        LOG_INFO("NavigationMowerNode publish MowerRunningState: {}", asStringLiteral(state));
        pub_nav_running_state_->publish(running_state);
    }
}

void NavigationMowerNode::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (mower_alg_)
    {
        mower_alg_->PublishException(level, value);
    }
}

void NavigationMowerNode::AlgorithmParamToConfigParam(const MowerAlgParam &param, NavigationMowerAlgConfig &config)
{
    config.is_enable_unstake_mode = param.is_enable_unstake_mode;
    config.unstake_distance = param.unstake_distance;
    config.unstake_adjust_yaw = param.unstake_adjust_yaw;
    config.unstake_vel_linear = param.unstake_vel_linear;
    config.unstake_vel_angular = param.unstake_vel_angular;

    config.mower_linear = param.mower_linear;
    config.mower_angular = param.mower_angular;
    // config.perception_drive_cooldown_time = param.perception_drive_cooldown_time;
    config.edge_mode_direction = param.edge_mode_direction;
    config.cross_region_adjust_yaw = param.cross_region_adjust_yaw;
    config.cross_region_adjust_displace = param.cross_region_adjust_displace;
    config.mark_distance_threshold = param.mark_distance_threshold;

    config.camera_2_center_dis = param.camera_2_center_dis;

    config.edge_perception_drive_cooldown_time_threshold = param.edge_perception_drive_cooldown_time_threshold;
    config.qr_detection_cooldown_time_threshold = param.qr_detection_cooldown_time_threshold;
    config.mark_detection_cooldown_time_threshold = param.mark_detection_cooldown_time_threshold;

    // test
    config.test_linear_speed = param.test_linear_speed;
    config.test_angular_speed = param.test_angular_speed;
    config.test_duration_ms = param.test_duration_ms;
    // 脱困检测数据记录控制
    config.enable_stuck_detection_data_logging = param.enable_stuck_detection_data_logging;
}

void NavigationMowerNode::ConfigParamToAlgorithmParam(const NavigationMowerAlgConfig &config, MowerAlgParam &param)
{
    param.is_enable_unstake_mode = config.is_enable_unstake_mode;
    param.unstake_distance = config.unstake_distance;
    param.unstake_adjust_yaw = config.unstake_adjust_yaw;
    param.unstake_vel_linear = config.unstake_vel_linear;
    param.unstake_vel_angular = config.unstake_vel_angular;

    param.mower_linear = config.mower_linear;
    param.mower_angular = config.mower_angular;
    // param.perception_drive_cooldown_time = config.perception_drive_cooldown_time;
    param.edge_mode_direction = config.edge_mode_direction;
    param.cross_region_adjust_yaw = config.cross_region_adjust_yaw;
    param.cross_region_adjust_displace = config.cross_region_adjust_displace;
    param.mark_distance_threshold = config.mark_distance_threshold;

    param.camera_2_center_dis = config.camera_2_center_dis;

    param.edge_perception_drive_cooldown_time_threshold = config.edge_perception_drive_cooldown_time_threshold;
    param.qr_detection_cooldown_time_threshold = config.qr_detection_cooldown_time_threshold;
    param.mark_detection_cooldown_time_threshold = config.mark_detection_cooldown_time_threshold;

    // test
    param.test_linear_speed = config.test_linear_speed;
    param.test_angular_speed = config.test_angular_speed;
    param.test_duration_ms = config.test_duration_ms;
    // Control of stuck detection data logging
    param.enable_stuck_detection_data_logging = config.enable_stuck_detection_data_logging;
}

void NavigationMowerNode::CloseAllTask()
{
    if (mower_alg_)
    {
        mower_alg_->SetAllTaskClose();
    }
}

void NavigationMowerNode::DealRechargeFinalResult(const fescue_msgs__msg__NavRechargeFinalResult &msg)
{
    if (msg.completed)
    {
        LOG_WARN("NavigationMowerNode recharge completed, result: {}", msg.result);
        SetMowerAppTriggersRecharge(false);
        SetMowerAppTriggersCrossRegion(false);
        SetMowerAppTriggersSpiralMower(false);
        SetMowerAppTriggersRegionExplore(false);
        SetMowerAppTriggersMower(false);
        SetMowerAppTriggersCutBorder(false);
        SetMowerComplete(true);
        CloseAllTask();
        SendRechargeFinalResult(msg.completed, msg.result); // Notify business layer, recharge is completed
    }
}

void NavigationMowerNode::DealCrossRegionFinalResult(const fescue_msgs__msg__NavCrossRegionFinalResult &msg)
{
    if (msg.completed)
    {
        LOG_WARN("NavigationMowerNode cross region completed, result: {}", msg.result);
        if (is_cross_region_start_)
        {
            SendCrossRegionFinalResult(msg.result); // Notify business layer, cross region is completed
        }
        SetMowerAppTriggersCrossRegion(false);
        PublishBeaconStatus(mower_msgs::msg::BeaconStatusType::EXIT);
    }
}

void NavigationMowerNode::DealSpiralMowerFinalResult(const ob_mower_msgs::NavSpiralMowerFinalResult &msg)
{
    if (msg.completed)
    {
        LOG_WARN("NavigationMowerNode spiral mower completed, result: {}", msg.success);
        SetMowerAppTriggersSpiralMower(false); // End spiral mowing
        SendSpiralMowerFinalResult(true, msg.success);
    }
}

void NavigationMowerNode::PublishPerceptionLocalizationAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &msg)
{
    if (pub_perception_localization_alg_ctrl_)
    {
        pub_perception_localization_alg_ctrl_->publish(msg);
    }
}

void NavigationMowerNode::PublishMCUTwist(const mower_msgs::msg::Twist &msg)
{
    if (pub_nav_twist_)
    {
        pub_nav_twist_->publishCopyOf(msg);
    }
}

void NavigationMowerNode::PublishBeaconStatus(mower_msgs::msg::BeaconStatusType status)
{
    if (pub_beacon_status_)
    {
        mower_msgs::msg::BeaconStatus beacon_status;
        beacon_status.status = status;
        pub_beacon_status_->publish(beacon_status);
    }
}

void NavigationMowerNode::SetLocalizationAreaEstimateState(ob_mower_msgs::PerceptionLocalizationAlgState state)
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgCtrl ctrl;
    ctrl.sender = "navigation_mower_node";
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION, state));
    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState state)
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgCtrl ctrl;
    ctrl.sender = "navigation_mower_node";
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, state));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, state));

    // area estimation is always enable
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION,
                                                                  PerceptionLocalizationAlgState::ENABLE));

    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnRecharge()
{
    SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnRechargeHaveFindQRCode()
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgState enable{PerceptionLocalizationAlgState::ENABLE};
    PerceptionLocalizationAlgState disable{PerceptionLocalizationAlgState::DISABLE};
    PerceptionLocalizationAlgCtrl ctrl;
    ctrl.sender = "navigation_mower_node";
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, disable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION, enable));
    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnRechargeFindingQRCode()
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgState enable{PerceptionLocalizationAlgState::ENABLE};
    PerceptionLocalizationAlgCtrl ctrl;
    ctrl.sender = "navigation_mower_node";
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION, enable));
    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnRandomMower()
{
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgState enable{PerceptionLocalizationAlgState::ENABLE};
    PerceptionLocalizationAlgState disable{PerceptionLocalizationAlgState::DISABLE};
    PerceptionLocalizationAlgCtrl ctrl;

    ctrl.sender = "navigation_mower_node";
    // enable algorithms
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, enable));

    // disable algorithms
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, disable));

    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnCrossRegion()
{
    // close charge station detection, station localization;
    using namespace fescue_iox::ob_mower_msgs;
    PerceptionLocalizationAlgState enable{PerceptionLocalizationAlgState::ENABLE};
    PerceptionLocalizationAlgState disable{PerceptionLocalizationAlgState::DISABLE};
    PerceptionLocalizationAlgCtrl ctrl;

    ctrl.sender = "navigation_mower_node";
    // enable algorithms
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_SEGMENT, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_FUSION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OBJECT_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CROSS_REGION_MARK_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_CROSS_REGION_MARK_LOCALIZATION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION, enable));
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION, enable));
    // close algorithms
    ctrl.ctrl_list.emplace_back(PerceptionLocalizationAlgCtrlData(PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION, disable));

    PublishPerceptionLocalizationAlgCtrl(ctrl);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnExploreMap()
{
    SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnCutBorder()
{
    SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE);
}

void NavigationMowerNode::SetPerceptionAndLocalizationStateOnSpiralMower()
{
    SetPerceptionAndLocalizationStateOnRandomMower();
}

bool NavigationMowerNode::GetMCUMissionType(mower_msgs::srv::MowerMissionType &mission_type)
{
    auto client = std::make_unique<IceoryxClientMower<get_mcu_mission_type_request, get_mcu_mission_type_response>>("get_mcu_mission_type");
    get_mcu_mission_type_request request_input;
    request_input.timestamp = GetSteadyClockTimestampMs();
    get_mcu_mission_type_response response_output;
    if (!client->SendRequest(request_input, response_output))
    {
        LOG_ERROR("NavigationMowerNode get mcu mission type failed!");
        return false;
    }
    mission_type = response_output.mcu_mission_type;
    return response_output.success;
}

bool NavigationMowerNode::SendAlgorithmVersion()
{
    int major{0};
    int minor{0};
    int patch{0};
    int year{0};
    int month{0};
    int day{0};
    int hour{0};
    int minute{0};
    int second{0};
    std::sscanf(_GIT_TAG_, "V%d.%d.%d", &major, &minor, &patch);
    std::sscanf(_COMPILE_TIME_, "%04d-%02d-%02d %02d:%02d:%02d", &year, &month, &day, &hour, &minute, &second);
    std::string git_commit_id = GetGitCommitIDPrefix8(std::string(_GIT_VERSION_));
    auto client = std::make_unique<IceoryxClientMower<alg_version_request, alg_version_response>>("alg_version");
    alg_version_request request_input;
    request_input.major_version = major;
    request_input.minor_version = minor;
    request_input.patch_version = patch;
    request_input.build_year = year;
    request_input.build_month = month;
    request_input.build_day = day;
    request_input.build_hour = hour;
    request_input.build_minute = minute;
    request_input.build_second = second;
    request_input.git_hash.unsafe_assign(git_commit_id.c_str());
    alg_version_response response_output;
    if (!client->SendRequest(request_input, response_output))
    {
        LOG_ERROR("NavigationMowerNode send algorithm version failed!");
        return false;
    }
    return response_output.success;
}

void NavigationMowerNode::OpenImuDataFile()
{
    std::string file_path = log_dir_ + "/imu_data.txt";
    imu_data_file_.open(file_path, std::ios::out);
    if (imu_data_file_.is_open())
    {
        LOG_INFO("IMU data file opened: {}", file_path.c_str());
        // Write header line
        imu_data_file_ << "timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,filtered_accel_x,filtered_accel_y,filtered_accel_z,filtered_gyro_x,filtered_gyro_y,filtered_gyro_z" << std::endl;
    }
    else
    {
        LOG_ERROR("Failed to open IMU data file: {}", file_path.c_str());
    }
}

void NavigationMowerNode::CloseImuDataFile()
{
    if (imu_data_file_.is_open())
    {
        imu_data_file_.close();
        LOG_INFO("IMU data file closed");
    }
}

void NavigationMowerNode::WriteImuDataFile(const mower_msgs::msg::SocImu &data)
{
    if (imu_data_file_.is_open())
    {
        imu_data_file_ << data.system_timestamp << ","
                       << data.linear_acceleration_x << ","
                       << data.linear_acceleration_y << ","
                       << data.linear_acceleration_z << ","
                       << data.angular_velocity_x << ","
                       << data.angular_velocity_y << ","
                       << data.angular_velocity_z << ","
                       << data.linear_acceleration_x << ","
                       << data.linear_acceleration_y << ","
                       << data.linear_acceleration_z << ","
                       << data.angular_velocity_x << ","
                       << data.angular_velocity_y << ","
                       << data.angular_velocity_z << std::endl;
    }
}

void NavigationMowerNode::WriteImuDataFile(const mower_msgs::msg::McuImu &data)
{
    if (imu_data_file_.is_open())
    {
        imu_data_file_ << data.system_timestamp << ","
                       << data.linear_acceleration_x << ","
                       << data.linear_acceleration_y << ","
                       << data.linear_acceleration_z << ","
                       << data.angular_velocity_x << ","
                       << data.angular_velocity_y << ","
                       << data.angular_velocity_z << ","
                       << data.linear_acceleration_x << ","
                       << data.linear_acceleration_y << ","
                       << data.linear_acceleration_z << ","
                       << data.angular_velocity_x << ","
                       << data.angular_velocity_y << ","
                       << data.angular_velocity_z << std::endl;
    }
}

void NavigationMowerNode::PublishFusionPose()
{
    if (pub_nav_fusion_pose_ && mower_alg_)
    {
        auto cur_fusion_pose = mower_alg_->GetFusionPose();
        fusion_pose_ = cur_fusion_pose;
        uint64_t time_now_ms = GetSteadyClockTimestampMs();
        uint64_t log_time_interval_ms = 2000;
        if (last_log_pose_time_ == 0 || time_now_ms - last_log_pose_time_ > log_time_interval_ms)
        {
            last_log_pose_time_ = time_now_ms;
            const auto &fusion_data = mower_alg_->GetFusionData();
            mower_msgs::msg::Twist velocity_data;
            {
                std::lock_guard<std::mutex> lock(velocity_data_mtx_);
                velocity_data = velocity_data_;
            }
            LOG_INFO("fusion_pose: timestamp: {}, x: {}, y: {}, yaw: {}, pitch: {}, roll: {} linear: {} angular: {} slope yaw: {} motor speed left: {} right: {} control linear: {} angular: {} mower state: {}",
                     cur_fusion_pose.timestamp_ms, cur_fusion_pose.x, cur_fusion_pose.y,
                     cur_fusion_pose.yaw, cur_fusion_pose.pitch, cur_fusion_pose.roll,
                     cur_fusion_pose.linear_velocity, cur_fusion_pose.angular_velocity,
                     fusion_data.slope_detection_result.yaw,
                     fusion_data.motor_speed_data.motor_speed_left,
                     fusion_data.motor_speed_data.motor_speed_right,
                     velocity_data.linear_velocity, velocity_data.angular_velocity,
                     asStringLiteral(mower_state_));
        }
        pub_nav_fusion_pose_->publish(cur_fusion_pose);
    }
}

} // namespace fescue_iox
