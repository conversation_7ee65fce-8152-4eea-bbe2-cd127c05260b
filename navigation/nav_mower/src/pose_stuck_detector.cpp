#include "pose_stuck_detector.hpp"

#include "utils/logger.hpp"

namespace fescue_iox
{

PoseStuckDetector::PoseStuckDetector(const PoseStuckConfig &config)
    : config_(config)
{
}

PoseStuckDetector::~PoseStuckDetector()
{
}

void PoseStuckDetector::Reset()
{
    std::deque<PoseStuckData> empty_pose_stuck_data;
    pose_stuck_data_.swap(empty_pose_stuck_data);
    std::deque<PoseStuckData> empty_collision_stuck_data;
    collision_stuck_data_.swap(empty_collision_stuck_data);
    std::deque<PoseStuckData> empty_slip_stuck_data;
    slip_stuck_data_.swap(empty_slip_stuck_data);
}

void PoseStuckDetector::Detect(const PoseStuckData &pose_stuck_data, PoseStuckResult *pose_stuck_result)
{
    if (!pose_stuck_result)
    {
        LOG_ERROR("pose_stuck_result is nullptr");
        return;
    }
    pose_stuck_result->is_pose_stuck = DetectPoseStuck(pose_stuck_data);
    pose_stuck_result->is_collision_stuck = DetectCollisionStuck(pose_stuck_data);
    pose_stuck_result->is_slip_stuck = DetectSlipStuck(pose_stuck_data);
}

bool PoseStuckDetector::DetectPoseStuck(const PoseStuckData &pose_stuck_data)
{
    if (pose_stuck_data_.empty())
    {
        pose_stuck_data_.push_back(pose_stuck_data);
        return false;
    }
    // check time interval
    uint64_t back_time_ms = pose_stuck_data_.back().timestamp_ms;
    uint64_t current_time_ms = pose_stuck_data.timestamp_ms;
    if (current_time_ms < back_time_ms || current_time_ms - back_time_ms < config_.interval_time_ms)
    {
        return false;
    }
    // update buffer
    pose_stuck_data_.push_back(pose_stuck_data);
    back_time_ms = pose_stuck_data_.back().timestamp_ms;
    uint64_t front_time_ms = pose_stuck_data_.front().timestamp_ms;
    while (back_time_ms - front_time_ms > config_.stuck_time_ms)
    {
        pose_stuck_data_.pop_front();
        if (pose_stuck_data_.empty())
        {
            return false;
        }
        front_time_ms = pose_stuck_data_.front().timestamp_ms;
        back_time_ms = pose_stuck_data_.back().timestamp_ms;
    }
    while (pose_stuck_data_.size() > config_.buffer_max_size)
    {
        pose_stuck_data_.pop_front();
    }
    if (pose_stuck_data_.size() < 2)
    {
        return false;
    }
    front_time_ms = pose_stuck_data_.front().timestamp_ms;
    back_time_ms = pose_stuck_data_.back().timestamp_ms;
    LOG_INFO_THROTTLE(500, "diff time: {} ms", back_time_ms - front_time_ms);
    if (back_time_ms - front_time_ms < config_.stuck_min_time_ms)
    {
        return false;
    }
    // detect stuck
    float angle_diff_sum = 0;
    for (size_t i = 0; i < pose_stuck_data_.size() - 1; ++i)
    {
        const auto &current_pose = pose_stuck_data_[i].pose;
        const auto &next_pose = pose_stuck_data_[i + 1].pose;
        float angle_diff = NormalizeAngle(next_pose.theta - current_pose.theta);
        angle_diff_sum += std::abs(angle_diff);
    }
    LOG_INFO_THROTTLE(500, "angle diff sum: {}", angle_diff_sum);
    if (angle_diff_sum < config_.stuck_min_angle)
    {
        Reset();
        return true;
    }
    return false;
}

bool PoseStuckDetector::DetectCollisionStuck(const PoseStuckData &pose_stuck_data)
{
    if (!pose_stuck_data.is_collision)
    {
        return false;
    }
    return DetectExceptionStuck(pose_stuck_data, "collision", true, 
                                config_.collision_stuck_interval_time_ms, config_.collision_stuck_distance, 
                                config_.collision_total_time_ms, config_.collision_stuck_total_time_ms, 
                                collision_stuck_data_);
}

bool PoseStuckDetector::DetectSlipStuck(const PoseStuckData &pose_stuck_data)
{
    if (!pose_stuck_data.is_slip)
    {
        return false;
    }
    return DetectExceptionStuck(pose_stuck_data, "slip", false, 
                                config_.slip_stuck_interval_time_ms, 0, 
                                config_.slip_total_time_ms, config_.slip_stuck_total_time_ms, 
                                slip_stuck_data_);
}

bool PoseStuckDetector::DetectExceptionStuck(const PoseStuckData &pose_stuck_data, 
                                             std::string name, bool check_distance, 
                                             uint64_t stuck_interval_time_ms, float stuck_distance, 
                                             uint64_t total_time_ms, uint64_t stuck_total_time_ms, 
                                             std::deque<PoseStuckData>& stuck_data)
{
    LOG_INFO("{} stuck data size: {}", name, stuck_data.size());
    if (stuck_data.empty())
    {
        stuck_data.push_back(pose_stuck_data);
        return false;
    }
    // check time interval
    uint64_t back_time_ms = stuck_data.back().timestamp_ms;
    uint64_t current_time_ms = pose_stuck_data.timestamp_ms;
    if (current_time_ms < back_time_ms || current_time_ms - back_time_ms < config_.min_interval_time_ms)
    {
        return false;
    }
    if (current_time_ms - back_time_ms > stuck_interval_time_ms)
    {
        LOG_INFO("{} stuck time interval is too long time interval: {}", name, current_time_ms - back_time_ms);
        std::deque<PoseStuckData> empty_stuck_data;
        stuck_data.swap(empty_stuck_data);
        stuck_data.push_back(pose_stuck_data);
        return false;
    }
    if (check_distance)
    {
        const auto& front_pose = stuck_data.front().pose;
        const auto& cur_pose = pose_stuck_data.pose;
        float distance = std::hypot(cur_pose.x - front_pose.x, cur_pose.y - front_pose.y);
        if (distance > stuck_distance)
        {
            LOG_INFO("{} stuck distance is too long", name);
            std::deque<PoseStuckData> empty_stuck_data;
            stuck_data.swap(empty_stuck_data);
            stuck_data.push_back(pose_stuck_data);
            return false;
        }
    }
    // update buffer
    stuck_data.push_back(pose_stuck_data);
    back_time_ms = stuck_data.back().timestamp_ms;
    uint64_t front_time_ms = stuck_data.front().timestamp_ms;
    while (back_time_ms - front_time_ms > total_time_ms)
    {
        stuck_data.pop_front();
        if (stuck_data.empty())
        {
            break;
        }
        front_time_ms = stuck_data.front().timestamp_ms;
        back_time_ms = stuck_data.back().timestamp_ms;
    }
    if (stuck_data.empty())
    {
        return false;
    }
    front_time_ms = stuck_data.front().timestamp_ms;
    back_time_ms = stuck_data.back().timestamp_ms;
    uint64_t time_diff = back_time_ms - front_time_ms;
    if (time_diff < stuck_total_time_ms)
    {
        LOG_INFO("{} stuck total time is too short: {} ms", name, time_diff);
        return false;
    }
    std::deque<PoseStuckData> empty_stuck_data;
    stuck_data.swap(empty_stuck_data);
    return true;
}

}