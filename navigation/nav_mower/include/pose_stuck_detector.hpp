#pragma once

#include <iostream>
#include <vector>
#include <deque>
#include <memory>
#include <algorithm>
#include <numeric>

#include "data_type.hpp"

namespace fescue_iox 
{

struct PoseStuckConfig
{
    // 缓存数据的时间间隔
    uint64_t interval_time_ms = 500;
    // 检测卡住的时间窗口
    uint64_t stuck_time_ms = 3 * 60 * 1000;
    // 检测卡住的最小时间窗口，比stuck_time_ms小一点
    uint64_t stuck_min_time_ms = 3 * 60 * 1000 - 5000;
    // 检测卡住的最小角度，即一段时间内机器正常运动应该转动超过此角度
    float stuck_min_angle = 2.09;
    // 缓冲区最大长度
    uint32_t buffer_max_size = 600;
    // 最小时间间隔
    uint64_t min_interval_time_ms = 500;
    // 碰撞卡住距离阈值
    float collision_stuck_distance = 0.5;
    // 碰撞卡住时间间隔
    uint64_t collision_stuck_interval_time_ms = 10 * 1000;
    // 碰撞卡住总时间
    uint64_t collision_stuck_total_time_ms = 50 * 1000;
    // 碰撞总时间
    uint64_t collision_total_time_ms = 60 * 1000;
    // 打滑卡住时间间隔
    uint64_t slip_stuck_interval_time_ms = 20 * 1000;
    // 打滑卡住总时间
    uint64_t slip_stuck_total_time_ms = 120 * 1000;
    // 打滑总时间
    uint64_t slip_total_time_ms = 130 * 1000;
};

struct PoseStuckData
{
    explicit PoseStuckData(uint64_t _timestamp_ms, float x, float y, float theta, bool _is_collision, bool _is_slip)
        : timestamp_ms(_timestamp_ms), pose(x, y, theta), is_collision(_is_collision), is_slip(_is_slip)
    {
    }

    uint64_t timestamp_ms = 0;
    Pose2f pose;
    bool is_collision = false;
    bool is_slip = false;
};

struct PoseStuckResult
{
    // 位姿角度长时间没有变化，需要强力脱困
    bool is_pose_stuck = false;
    // 在小范围内长时间触发碰撞，需要报错
    bool is_collision_stuck = false;
    // 长时间打滑，需要报错
    bool is_slip_stuck = false;
};

class PoseStuckDetector
{
public:
    PoseStuckDetector(const PoseStuckConfig &config);
    ~PoseStuckDetector();

    void Detect(const PoseStuckData &pose_stuck_data, PoseStuckResult *pose_stuck_result);

    void Reset();

private:
    bool DetectPoseStuck(const PoseStuckData &pose_stuck_data);
    bool DetectSlipStuck(const PoseStuckData &pose_stuck_data);
    bool DetectCollisionStuck(const PoseStuckData &pose_stuck_data);
    bool DetectExceptionStuck(const PoseStuckData &pose_stuck_data, 
                             std::string name, bool check_distance, 
                             uint64_t stuck_interval_time_ms, float stuck_distance, 
                             uint64_t total_time_ms, uint64_t stuck_total_time_ms, 
                             std::deque<PoseStuckData>& stuck_data);

private:
    PoseStuckConfig config_;
    std::deque<PoseStuckData> pose_stuck_data_;
    std::deque<PoseStuckData> collision_stuck_data_;
    std::deque<PoseStuckData> slip_stuck_data_;
};

}