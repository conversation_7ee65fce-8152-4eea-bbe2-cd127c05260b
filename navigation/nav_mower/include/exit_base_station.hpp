#pragma once

#include <iostream>
#include <memory>
#include <algorithm>
#include <numeric>

#include "data_type.hpp"
#include "mower_msgs/msg/soc_exception.hpp"

namespace fescue_iox 
{

enum class ExitBaseStationState
{
    INVALID,
    WAIT,
    BACK_TO_GRASS,
    EXIT_FROM_BASE_STATION,
    EXIT_BY_DISTANCE,
    TURN_ANGLE,
    CHECK_GRASS,
    FINISH,
};

struct ExitBaseStationConfig
{
    uint64_t wait_time_ms = 2000;
    float non_grass_duration_s = 1.0;
    uint64_t back_to_grass_time_ms = 5000;
    float back_to_grass_linear_velocity = -0.1;
    float back_to_grass_angular_velocity = 0.0;
    float back_from_base_station_linear_velocity = -0.2;
    float back_from_base_station_angular_velocity = 0.0;
    uint64_t back_from_base_station_time_ms = 5000;
    float turn_angle_linear_velocity = 0.0;
    float turn_angle_angular_velocity = 0.5;
    uint64_t turn_angle_time_ms = 2000;
    float back_by_distance_linear_velocity = -0.2;
    float back_by_distance_angular_velocity = 0.0;
    float near_base_station_distance = 1.0;
    uint64_t exit_by_distance_time_ms = 2000;
    float check_grass_linear_velocity = 0.0;
    float check_grass_angular_velocity = 0.0;
    uint64_t check_grass_time_ms = 1000;
};

struct ExitBaseStationResult
{
    bool is_exit_success = false;
    bool is_need_exit = false;
    float linear_velocity = 0.0;
    float angular_velocity = 0.0;
    bool is_need_publish_exception = false;
    mower_msgs::msg::SocExceptionLevel exception_level = mower_msgs::msg::SocExceptionLevel::NONE;
    mower_msgs::msg::SocExceptionValue exception_value = mower_msgs::msg::SocExceptionValue::NO_EXCEPTION;
};

class ExitBaseStationAlg
{
public:
    ExitBaseStationAlg(const ExitBaseStationConfig &config) : config_(config) {}
    ~ExitBaseStationAlg() = default;

    ExitBaseStationResult Run(bool is_power_connected, bool is_on_grass_field, const std::chrono::seconds& non_grass_duration, 
                              const QRCodeLocationResult &qrcode_loc_result);
    void Reset();

private:
    bool IsOnGrass(bool is_power_connected, bool is_on_grass_field, const std::chrono::seconds& non_grass_duration) const;

private:
    ExitBaseStationConfig config_;
    ExitBaseStationState state_{ExitBaseStationState::INVALID};
    uint64_t wait_start_time_ms_{0};
    uint64_t back_to_grass_start_time_ms_{0};
    uint64_t exit_from_base_station_start_time_ms_{0};
    uint64_t exit_by_distance_start_time_ms_{0};
    uint64_t turn_angle_start_time_ms_{0};
    uint64_t check_grass_start_time_ms_{0};
};

}