#pragma once

#include "calibration_bev.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "mower_msgs/srv/calibration_result.hpp"
#include "mower_msgs/srv/calibration_result_eeprom.hpp"
#include "mower_msgs/srv/camera_bev_params.hpp"
#include "mower_msgs/srv/camera_intrinsic.hpp"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "ob_mower_srvs/algorithm_version_service__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_header.hpp"

#include <chrono>
#include <cmath>
#include <condition_variable>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

using namespace fescue_iox::ob_mower_srvs;

namespace fescue_iox
{

using get_node_param_request = GetNodeParamRequest;
using get_node_param_response = GetNodeParamResponse;
using set_node_param_request = SetNodeParamRequest;
using set_node_param_response = SetNodeParamResponse;

using get_calib_result_request = mower_msgs::srv::GetCalibResultRequest;
using get_calib_result_response = mower_msgs::srv::GetCalibRequestResponse;
using start_calib_request = mower_msgs::srv::StartCalibRequest;
using start_calib_response = mower_msgs::srv::StartCalibResponse;

using SocImu = mower_msgs::msg::SocImu;

using get_alg_version_request = fescue_msgs__srv__GetAlgorithmVersionData_Request;
using get_alg_version_response = fescue_msgs__srv__GetAlgorithmVersionData_Response;

class CalibrationBevNode
{
public:
    CalibrationBevNode(const std::string& node_name);
    ~CalibrationBevNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitLogger();
    void InitAlgorithm();
    void InitSubscriber();
    void InitService();
    void InitHeartbeat();

private:
    void Deal1280x720Image(const sensor_msgs__msg__Image_iox& data);
    void DealImu(const SocImu& data);
    int ProcessStartCalib(const start_calib_request& request);
    int StartBevCalib();
    int StartImuCalib();
    void EncodeImage(const cv::Mat& image, mower_msgs::srv::CalibrationBevImage& img);
    bool ProcessGetAlgoVersion(get_alg_version_response& response);
    int ProcessGetCalibResult(get_calib_result_response& response);
    bool GetNodeParam(NodeParamData& data);
    bool SetNodeParam(const NodeParamData& data);
    bool GetIntrinsicsParam(RGBCameraIntrinsic& cam_intrinsics);
    bool SetCalibResult(mower_msgs::srv::BEVCalibParams& bev_params, mower_msgs::srv::BEVCalibParams2& bev_params_2,
                        mower_msgs::srv::IMUCalibParams& imu_params, bool bev_calib_result, bool imu_calib_result);

private:
    // subscriber
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_1280x720_image_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<SocImu>> sub_soc_imu_{nullptr};

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<start_calib_request, start_calib_response>> service_start_calib_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_calib_result_request, get_calib_result_response>> service_get_calib_result_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_version_request, get_alg_version_response>> service_get_alg_version_{nullptr};

    std::mutex mutex_;
    std::condition_variable cv_;
    bool start_bev_calib_{false};
    bool start_imu_calib_{false};

    // config params
    std::string node_name_{"calibration_bev_node"};
    std::string log_dir_{""};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string calibration_alg_conf_file_{};
    int compress_quality_{80};
    std::string compress_type_{"jpg"};

    // alg ptr
    std::unique_ptr<CalibrationBevAlg> calibration_bev_alg_{nullptr};
    cv::Mat cali_img_;
    cv::Mat encode_img_;
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
    std::map<std::string, std::string> algo_version_map_;
    RGBCameraIntrinsic intrinsic_params_;
    bool has_intrinsic_{false};

    static constexpr int CALIB_SUCCESS = 0;
    static constexpr int CALIB_ERR_NO_ALG = -1;
    static constexpr int CALIB_ERR_INTRINSIC = -2;
    static constexpr int CALIB_ERR_WRITE_EEPROM = -3;
    static constexpr int CALIB_ERR_TIMEOUT = -4;
    static constexpr int CALIB_ERR_NO_IMAGE = -5;
    static constexpr int CALIB_FAIL = -6;
    static constexpr auto CALIB_WAIT_TIMEOUT = std::chrono::milliseconds(500);
};

} // namespace fescue_iox
