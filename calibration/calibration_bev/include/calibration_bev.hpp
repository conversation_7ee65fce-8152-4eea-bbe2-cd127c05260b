#pragma once

#include "BEVCalibAPI.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "opencv2/opencv.hpp"
#include "sensor_msgs/image__struct.h"

#include <string>
#include <vector>

namespace fescue_iox
{

class CalibrationBevAlg
{
public:
    CalibrationBevAlg(const std::string& conf_file);
    ~CalibrationBevAlg();
    void SetCalibParam(MowerSystemCalibConfig& conf);
    int ProcessImg(const cv::Mat& image, uint32_t camera_flag, RGBCameraIntrinsic& cam_intrinsics);
    int ProcessImu(const uint64_t timestamp, std::vector<double> acceleration, std::vector<double> gyroscope);
    int ProcessCalib(MowerSystemCalibResult& calib_result);
    const std::string GetAlgVersion() const;

private:
    void InitAlg();
    void DeinitAlg();

private:
    std::string conf_file_{""};
    MOWER_SYSTEM_CALIB_HANDLE handle_{nullptr};
};

} // namespace fescue_iox
