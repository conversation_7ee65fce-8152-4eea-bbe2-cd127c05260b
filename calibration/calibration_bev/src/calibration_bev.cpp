#include "calibration_bev.hpp"

#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

CalibrationBevAlg::CalibrationBevAlg(const std::string& conf_file)
    : conf_file_(conf_file)
{
    InitAlg();
}

CalibrationBevAlg::~CalibrationBevAlg()
{
    DeinitAlg();
}

void CalibrationBevAlg::SetCalibParam(MowerSystemCalibConfig& conf)
{
    MSCAL_set_config_parameter(handle_, &conf);
}

int CalibrationBevAlg::ProcessImg(const cv::Mat& image, uint32_t camera_flag, RGBCameraIntrinsic& cam_intrinsics)
{
    uint32_t img_width = image.cols;
    uint32_t img_height = image.rows;
    uint8_t* img_data = image.data;

    ErrCode err_code = MSCAL_add_Image(handle_, img_width, img_height, img_data, &cam_intrinsics, camera_flag);
    if (err_code != CALIB_OK) {
        LOG_ERROR("CalibrationBevAlg MSCAL_add_Image error, err_code: {}!", static_cast<int>(err_code));
        return static_cast<int>(err_code);
    }

    return 0;
}

int CalibrationBevAlg::ProcessImu(const uint64_t timestamp, std::vector<double> acceleration, std::vector<double> gyroscope)
{
    ErrCode err_code = MSCAL_add_IMU(handle_, timestamp, acceleration[0], acceleration[1], acceleration[2],
                                     gyroscope[0], gyroscope[1], gyroscope[2]);
    if (err_code != CALIB_OK && err_code != CALIB_CONTINUE_IMU) {
        LOG_ERROR("CalibrationBevAlg MSCAL_add_IMU error, err_code: {}!", static_cast<int>(err_code));
        return static_cast<int>(err_code);
    }

    return 0;
}

int CalibrationBevAlg::ProcessCalib(MowerSystemCalibResult& calib_result)
{
    ErrCode err_code = MSCAL_calib(handle_, &calib_result);
    if (err_code != CALIB_OK) {
        LOG_ERROR("CalibrationBevAlg MSCAL_calib error, err_code: {}!", static_cast<int>(err_code));
        return static_cast<int>(err_code);
    }

    return 0;
}

const std::string CalibrationBevAlg::GetAlgVersion() const
{
    char version[128] = {0};
    if (GetVersion_BEVCalib(version)) {
        return std::string(version);
    }
    return "unknown";
}

void CalibrationBevAlg::InitAlg()
{
    ErrCode err = MSCAL_create(&handle_, conf_file_.c_str());
    if (err != CALIB_OK) {
        LOG_ERROR("Calibration alg create failed, error code: {:X}", static_cast<int>(err));
        return;
    }

    char version[128] = {0};
    if (GetVersion_BEVCalib(version)) {
        LOG_INFO("Calibration alg version: {}", version);
    } else {
        LOG_ERROR("Get calibration alg version fail!");
    }
}

void CalibrationBevAlg::DeinitAlg()
{
    MSCAL_release(handle_);
}

} // namespace fescue_iox
