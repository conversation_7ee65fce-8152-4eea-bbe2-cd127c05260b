#include "calibration_bev_node.hpp"

#include "calibration_bev_node_config.hpp"
#include "mower_sdk_version.h"
#include "opencv2/opencv.hpp"
#include "utils/dir.hpp"
#include "utils/file.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{

CalibrationBevNode::CalibrationBevNode(const std::string& node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitAlgorithm();
    InitSubscriber();
    InitService();
    InitHeartbeat();
}

CalibrationBevNode::~CalibrationBevNode()
{
    LOG_WARN("CalibrationBevNode stop success!");
}

void CalibrationBevNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void CalibrationBevNode::InitParam()
{
    const std::string conf_file = "conf/" + node_name_ + "/" + node_name_ + ".yaml";
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty()) {
        LOG_INFO("CalibrationBevNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path)) {
            LOG_ERROR("CalibrationBevNode create config path failed!!!");
        }
    }
    if (!Config<CalibrationBevNodeConfig>::Init(conf_file)) {
        LOG_WARN("Init CalibrationBevNode config parameters failed!");
    }
    CalibrationBevNodeConfig config = Config<CalibrationBevNodeConfig>::GetConfig();
    LOG_INFO("[calibration_bev_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[calibration_bev_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[calibration_bev_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    calibration_alg_conf_file_ = config.calibration_alg_conf_file;
    compress_quality_ = config.compress_quality;
    compress_type_ = config.compress_type;

    if (!Config<CalibrationBevNodeConfig>::SetConfig(config, true)) {
        LOG_WARN("Set CalibrationBevNode config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void CalibrationBevNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void CalibrationBevNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void CalibrationBevNode::InitAlgorithm()
{
    calibration_bev_alg_ = std::make_unique<CalibrationBevAlg>(calibration_alg_conf_file_);
    algo_version_map_["calibration_bev"] = calibration_bev_alg_->GetAlgVersion();
}

void CalibrationBevNode::InitSubscriber()
{
    sub_1280x720_image_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_1280x720_result", 1, [this](const sensor_msgs__msg__Image_iox& data, const std::string& event) {
            (void)event;
            Deal1280x720Image(data);
        });
    sub_soc_imu_ = std::make_unique<IceoryxSubscriberMower<SocImu>>(
        "soc_imu", 1, [this](const SocImu& imu, const std::string& event) {
            (void)event;
            DealImu(imu);
        });
}

void CalibrationBevNode::Deal1280x720Image(const sensor_msgs__msg__Image_iox& img)
{
    std::unique_lock<std::mutex> lck(mutex_);
    if (start_bev_calib_) {
        NV12ToGrayMat(cali_img_, img.data.data(), img.width, img.height);
        start_bev_calib_ = false;
        cv_.notify_all();
    }
}

void CalibrationBevNode::DealImu(const SocImu& data)
{
    if (start_imu_calib_ && calibration_bev_alg_) {
        uint64_t timestamp_ns = data.frame_timestamp * 1e6;
        std::vector<double> acceleration{data.linear_acceleration_x, data.linear_acceleration_y, data.linear_acceleration_z};
        std::vector<double> gyroscope{data.angular_velocity_x, data.angular_velocity_y, data.angular_velocity_z};
        calibration_bev_alg_->ProcessImu(timestamp_ns, acceleration, gyroscope);
    }
}

void CalibrationBevNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_calibration_node_param", 10U, [this](const get_node_param_request& request, get_node_param_response& response) {
            (void)request;
            response.success = GetNodeParam(response.data);
            LOG_INFO("Get calibration bev node param execute {}", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_calibration_node_param", 10U, [this](const set_node_param_request& request, set_node_param_response& response) {
            response.success = SetNodeParam(request.data);
            LOG_INFO("Set calibration bev node param execute {}", response.success);
        });
    service_start_calib_ = std::make_unique<IceoryxServerMower<start_calib_request, start_calib_response>>(
        "start_calib", 10U, [this](const start_calib_request& request, start_calib_response& response) {
            response.success = ProcessStartCalib(request);
            LOG_INFO("Start calibration execute {}", response.success);
        });
    service_get_calib_result_ = std::make_unique<IceoryxServerMower<get_calib_result_request, get_calib_result_response>>(
        "get_calib_result", 10, [this](const get_calib_result_request& request, get_calib_result_response& response) {
            (void)request;
            response.success = ProcessGetCalibResult(response);
            LOG_INFO("Calibration result is {}", response.success);
        });
    service_get_alg_version_ = std::make_unique<IceoryxServerMower<get_alg_version_request, get_alg_version_response>>(
        "get_calib_algo_version", 10U,
        [this](const get_alg_version_request& request, get_alg_version_response& response) {
            (void)request;
            response.success = ProcessGetAlgoVersion(response);
            LOG_INFO("Get algorithm fusion alg version execute {}", response.success);
        });
}

bool CalibrationBevNode::ProcessGetAlgoVersion(get_alg_version_response& response)
{
    for (auto it = algo_version_map_.begin(); it != algo_version_map_.end(); ++it) {
        fescue_msgs__msg__AlgorithmVersionData version_data;
        version_data.name.unsafe_assign(it->first.c_str());
        version_data.version.unsafe_assign(it->second.c_str());
        response.data.push_back(version_data);
    }
    return true;
}

int CalibrationBevNode::ProcessGetCalibResult(get_calib_result_response& response)
{
    int ret = CALIB_SUCCESS;
    bool bev_calib_result{false};
    bool imu_calib_result{false};

    if (!calibration_bev_alg_) {
        return CALIB_ERR_NO_ALG;
    }

    MowerSystemCalibResult result;
    ret = calibration_bev_alg_->ProcessCalib(result);
    if (ret != 0) {
        LOG_ERROR("MSCAL_calib return fail, ret = {}", ret);
        goto PROCESS_CALIB_FAIL;
    }

    start_imu_calib_ = false;

    response.BEV_calib_result = result.bev_calib_result;
    response.IMU_calib_result = result.imu_calib_result;

    LOG_INFO("************ Calib result: {}, BEV_calib_result: {}, IMU_calib_result: {} ************", result.calib_success,
             result.bev_calib_result, result.imu_calib_result);

    // BEV result
    if (result.bev_calib_result > 0) {
        bev_calib_result = true;
        LOG_INFO("result.img_width_pixel: {} result.img_height_pixel: {}", result.img_width_pixel, result.img_height_pixel);
        response.bev_params.img_width_ = result.img_width_pixel;
        response.bev_params.img_height_ = result.img_height_pixel;
        response.bev_params.scotoma_distance_ = result.scotoma_distance;
        response.bev_params.bev_physical_width_ = result.bev_physical_width;
        response.bev_params.bev_physical_lenght_ = result.bev_physical_length;
        response.bev_params.top_left_pt_x = result.top_left_x_pixel;
        response.bev_params.top_left_pt_y = result.top_left_y_pixel;
        response.bev_params.bottom_left_pt_x = result.bottom_left_x_pixel;
        response.bev_params.bottom_left_pt_y = result.bottom_left_y_pixel;
        response.bev_params.top_right_pt_x = result.top_right_x_pixel;
        response.bev_params.top_right_pt_y = result.top_right_y_pixel;
        response.bev_params.bottom_right_pt_x = result.bottom_right_x_pixel;
        response.bev_params.bottom_right_pt_y = result.bottom_right_y_pixel;
        response.bev_params_2.trans_front_cam_x = result.trans_front_cam_x;
        response.bev_params_2.trans_front_cam_y = result.trans_front_cam_y;
        response.bev_params_2.trans_front_cam_z = result.trans_front_cam_z;
        response.bev_params_2.rot_front_cam_qx = result.rot_front_cam_qx;
        response.bev_params_2.rot_front_cam_qy = result.rot_front_cam_qy;
        response.bev_params_2.rot_front_cam_qz = result.rot_front_cam_qz;
        response.bev_params_2.rot_front_cam_qw = result.rot_front_cam_qw;
    }
    // IMU result
    if (result.imu_calib_result > 0) {
        imu_calib_result = true;
        response.imu_params.imu_calib_type = result.imu_calib_result;
        response.imu_params.trans_front_imu_x = result.trans_front_imu_x;
        response.imu_params.trans_front_imu_y = result.trans_front_imu_y;
        response.imu_params.trans_front_imu_z = result.trans_front_imu_z;
        response.imu_params.rot_front_imu_qx = result.rot_front_imu_qx;
        response.imu_params.rot_front_imu_qy = result.rot_front_imu_qy;
        response.imu_params.rot_front_imu_qz = result.rot_front_imu_qz;
        response.imu_params.rot_front_imu_qw = result.rot_front_imu_qw;
        response.imu_params.acc_bias_x = result.acc_bias_x;
        response.imu_params.acc_bias_y = result.acc_bias_y;
        response.imu_params.acc_bias_z = result.acc_bias_z;
        response.imu_params.gyr_bias_x = result.gyr_bias_x;
        response.imu_params.gyr_bias_y = result.gyr_bias_y;
        response.imu_params.gyr_bias_z = result.gyr_bias_z;
    }

    if (result.calib_success) {
        // Send calib result to eeprom
        if (!SetCalibResult(response.bev_params, response.bev_params_2,
                            response.imu_params, bev_calib_result, imu_calib_result)) {
            ret = CALIB_ERR_WRITE_EEPROM;
            LOG_ERROR("************ Calib execute success, but set calib result to eeprom fail *************");
            goto PROCESS_CALIB_FAIL;
        } else {
            // Calibration success......
            ret = CALIB_SUCCESS;
        }
    } else {
        ret = CALIB_FAIL;
    }

PROCESS_CALIB_FAIL:
    ErrCode err_code = static_cast<ErrCode>(ret);
    if (err_code != CALIB_CONTINUE_IMU && result.bev_calib_result != 0 && !encode_img_.empty()) {
        EncodeImage(encode_img_, response.image);
        encode_img_.release();
    }

    return ret;
}

int CalibrationBevNode::ProcessStartCalib(const start_calib_request& request)
{
    if (!calibration_bev_alg_) {
        return CALIB_ERR_NO_ALG;
    }

    int ret = 0;
    MowerSystemCalibConfig conf;
    conf.saveResult = request.save_result;
    conf.calibBEV = request.calib_bev;
    conf.calibIMU = request.calib_imu;
    calibration_bev_alg_->SetCalibParam(conf);

    if (0 != request.calib_bev && 0 != request.calib_imu) {
        StartImuCalib();
        ret = StartBevCalib();
    } else if (0 == request.calib_bev && 0 != request.calib_imu) {
        ret = StartImuCalib();
    } else if (0 != request.calib_bev && 0 == request.calib_imu) {
        ret = StartBevCalib();
    }

    return ret;
}

int CalibrationBevNode::StartBevCalib()
{
    if (!calibration_bev_alg_) {
        return CALIB_ERR_NO_ALG;
    }

    {
        std::unique_lock<std::mutex> lock(mutex_);
        cali_img_.release();
        start_bev_calib_ = true;
        bool wait_result = cv_.wait_for(lock, CALIB_WAIT_TIMEOUT, [this] { return !start_bev_calib_; });
        if (!wait_result) {
            LOG_ERROR("Calibration bev wait for image timeout!");
            start_bev_calib_ = false;
            return CALIB_ERR_TIMEOUT;
        }
        if (cali_img_.empty()) {
            LOG_ERROR("Calibration bev get image fail!");
            return CALIB_ERR_NO_IMAGE;
        }
    }

    encode_img_.release();
    cv::Mat img_cali;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        img_cali = cali_img_.clone();
        encode_img_ = cali_img_.clone();
    }

    // get intrinsic params
    if (!has_intrinsic_) {
        if (!GetIntrinsicsParam(intrinsic_params_)) {
            LOG_ERROR("CalibrationBevNode get intrinsics param fail!");
            return CALIB_ERR_INTRINSIC;
        } else {
            has_intrinsic_ = true;
        }
    }

    int ret = calibration_bev_alg_->ProcessImg(img_cali, 0, intrinsic_params_);
    if (0 != ret) {
        LOG_ERROR("CalibrationBevNode execute calibration algo fail!");
    }

    LOG_INFO("********************** start calib bev success! *************************");

    return ret;
}

int CalibrationBevNode::StartImuCalib()
{
    start_imu_calib_ = true;
    LOG_INFO("********************** start calib imu success! *************************");
    return 0;
}

void CalibrationBevNode::EncodeImage(const cv::Mat& image, mower_msgs::srv::CalibrationBevImage& img)
{
    std::vector<uchar> compress_data;
    std::vector<int> compression_params = {compress_type_ == "png" ? cv::IMWRITE_PNG_COMPRESSION : cv::IMWRITE_JPEG_QUALITY, compress_quality_}; // 设置JPEG压缩质量（0-100）
    std::string encode_type = (compress_type_ == "png" ? ".png" : ".jpg");
    cv::imencode(encode_type, image, compress_data, compression_params);
    size_t img_size = (compress_data.size() > mower_msgs::srv::BEV_IMAGE_DATA_MAX) ? mower_msgs::srv::BEV_IMAGE_DATA_MAX : compress_data.size();
    img.size = img_size;
    img.width = image.cols;
    img.height = image.rows;
    img.channel = image.channels();
    img.type = (compress_type_ == "png" ? 1 : 0);
    img.data.resize(img_size);
    memcpy(img.data.data(), compress_data.data(), img_size);
}

bool CalibrationBevNode::GetNodeParam(NodeParamData& data)
{
    CalibrationBevNodeConfig config = Config<CalibrationBevNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool CalibrationBevNode::SetNodeParam(const NodeParamData& data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    CalibrationBevNodeConfig config = Config<CalibrationBevNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<CalibrationBevNodeConfig>::SetConfig(config);
    LOG_INFO("New CalibrationBevNode params: {}", config.toString().c_str());
    return true;
}

bool CalibrationBevNode::GetIntrinsicsParam(RGBCameraIntrinsic& param)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraIntrinsicRequest,
                                                      mower_msgs::srv::CameraIntrinsicResponse>>("get_union_rgb_camera_intrinsic");
    mower_msgs::srv::CameraIntrinsicRequest request;
    mower_msgs::srv::CameraIntrinsicResponse response;
    if (!client->SendRequest(request, response)) {
        return false;
    }
    param.model_ = response.camera_intrinsic.model_;
    param.img_width_pixel = response.camera_intrinsic.img_width_;
    param.img_height_pixel = response.camera_intrinsic.img_height_;
    param.focal_x_ = response.camera_intrinsic.focal_x_;
    param.focal_y_ = response.camera_intrinsic.focal_y_;
    param.cx_ = response.camera_intrinsic.cx_;
    param.cy_ = response.camera_intrinsic.cy_;
    param.k1_ = response.camera_intrinsic.k1_;
    param.k2_ = response.camera_intrinsic.k2_;
    param.k3_ = response.camera_intrinsic.k3_;
    param.k4_ = response.camera_intrinsic.k4_;
    if (param.model_ == 0) // K6
    {
        param.k5_ = response.camera_intrinsic.k5_;
        param.k6_ = response.camera_intrinsic.k6_;
        param.p1_ = response.camera_intrinsic.p1_;
        param.p2_ = response.camera_intrinsic.p2_;
    } else { // KB
        param.k5_ = 0.0;
        param.k6_ = 0.0;
        param.p1_ = 0.0;
        param.p2_ = 0.0;
    }
    return true;
}

bool CalibrationBevNode::SetCalibResult(mower_msgs::srv::BEVCalibParams& bev_params,
                                        mower_msgs::srv::BEVCalibParams2& bev_params_2,
                                        mower_msgs::srv::IMUCalibParams& imu_params,
                                        bool bev_calib_result,
                                        bool imu_calib_result)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::SetCalibReusltEepromRequest,
                                                      mower_msgs::srv::SetCalibResultEepromResponse>>("set_union_rgb_camera_all_params");
    mower_msgs::srv::SetCalibReusltEepromRequest request;
    mower_msgs::srv::SetCalibResultEepromResponse response;
    request.bev_calib_result = bev_calib_result;
    request.imu_calib_result = imu_calib_result;
    request.bev_params = bev_params;
    request.bev_params_2 = bev_params_2;
    request.imu_params = imu_params;
    if (!client->SendRequest(request, response, 5)) {
        return false;
    }
    return response.success;
}

} // namespace fescue_iox
