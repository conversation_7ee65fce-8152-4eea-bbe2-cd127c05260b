%YAML:1.0 # need to specify the file type at the top!

#CameraModel: Brown = 0; KB = 1; MEI = 2; DS = 3
# cameraModel: 1
# # # fx fy cx fy
# intrinsics:  [543.64776611328125, 543.56170654296875, 635.3592529296875, 373.6883544921875]
# # k1 k2 k3 k4 or k1 ~ k6 p1 p2
# distortion_coeffs: [-0.034882165491580963, 0.0027219548355787992, -0.00061016308609396219, -0.0001422128698322922]
# resolution: [1280, 720]

# calib BEV
calib_BEV: 1
open_calib_accracy_evaluation: 1
#unit: m
output_resolution: [640,360]
calib_board_position: 0.6
board_center_height: 0.075
calib_BEV_x_test: 1.0
calib_BEV_y_test: 0.9
calib_BEV_x_far: 0.4
calib_BEV_x_near: 0.4
calib_BEV_y_left: 0.9
calib_BEV_y_right: 0.9
# calib_BEV_y_max: 1.0
# calib_BEV_y_min: 0.1

# if ArUco
arUcoID_dictionary: DICT_APRILTAG_25h9
arUcoID: [0,1,2,3]
# unit: m
arUcoScale: [0.06, 0.06]
# The origin is in the upper left corner
arUcoGrid_cols: 2
arUcoGrid_rows: 2
# unit: m
arUcoGrid_width: 0.150
arUcoGrid_height: 0.150
arUcoGrid_edge: 0.01

# evaluation
# left,right
arUcoID_rank_1: [4,5]
arUcoID_rank_2: [6,7]
arUcoID_rank_3: [8,9]
# unit: m
arUcoScale_rank: 0.08
arUcoEdge_rank: 0.01
# unit: m
rank_1_dis_to_calib: 0.20
rank_1_width: 0.5
rank_2_dis_to_calib: 0.45
rank_2_width: 0.5
rank_3_dis_to_calib: 0.9
rank_3_width: 0.5

# unit: pixel
evaluationThre: 6.0



# calib IMU
calib_IMU: 1 #0:not use 1:Align to the camera 2:Align to the body 3:Align to the ground



# show image
showImg: false

# debug write image
writeImg: true
# /data/GRASS/BEVCALIB/tmp  # /userdata/image/calibration_node
writeImgPath: /userdata/image/calibration_node

# 0 -- ALL; 1 -- DEBUG; 2 -- INFO; 3 -- WARNING; 4 -- ERROR; 5 -- SILENT
verbosity: 1
