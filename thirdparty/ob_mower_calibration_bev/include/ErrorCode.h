/*****************************************************************************
 *  Orbbec Vision Core Library
 *  Copyright (C) 2017 by ORBBEC Technology., Inc.
 *
 *  This file is part of Orbbec Vision Core Library 2.0
 *
 *  This file belongs to ORBBEC Technology., Inc.
 *  It is considered a trade secret, and is not to be divulged or used by
 *  parties who have NOT received written authorization from the owner.
 ****************************************************************************/

#ifndef __ERRORCODE_H__
#define __ERRORCODE_H__

#ifdef __cplusplus
extern "C"
{
#endif

#ifdef _WIN32
#ifdef VCAL_EXPORTS
#define VCAL_API __declspec(dllexport)
#else
#define VCAL_API __declspec(dllimport)
#endif
#else
#define VCAL_API
#endif

  typedef enum
  {
    CALIB_OK = 0,
    CALIB_NG = 1, /* NG */

    CALIB_ERR_CREATE_MSCAL = 2,
    CALIB_ERR_LOAD_CONFIG_FILE = 3,
    CALIB_ERR_CONFIG_PARAMS = 4,
    CALIB_ERR_CREATE_LOG_FOLDER = 5,

    CALIB_ERR_NOT_CALIB_BEV = 6,
    CALIB_ERR_LOAD_CAM_PARAMS = 7,
    CALIB_ERR_DETECT_IMG_NOT_8UC1 = 8,
    CALIB_ERR_DETECT_ARUCO = 9,
    CALIB_ERR_CALC_OBJ_PTS = 10,
    CALIB_ERR_CALC_BEV_PARAMS = 11,
    CALIB_ERR_BEV_ACC_EVALUATE = 12,
    CALIB_ERR_SAVE_BEV_RESULT = 13,

    CALIB_CONTINUE_IMU = 14,//need more IMU
    CALIB_ERR_CALC_IMU_PARAMS = 15,
    CALIB_ERR_SAVE_IMU_RESULT = 16,

  } ErrCode;

#ifdef __cplusplus
}
#endif

#endif //__ERRORCODE_H__