/*****************************************************************************
 *  Orbbec Vision Core Library
 *  Copyright (C) 2017 by ORBBEC Technology., Inc.
 *
 *  This file is part of Orbbec Vision Core Library 2.0
 *
 *  This file belongs to ORBBEC Technology., Inc.
 *  It is considered a trade secret, and is not to be divulged or used by
 *  parties who have NOT received written authorization from the owner.
 ****************************************************************************/

#ifndef __BEVCALIBAPI_H__
#define __BEVCALIBAPI_H__

#include <iostream>
#include "ErrorCode.h"
// #include <opencv2/opencv.hpp>

#ifdef __cplusplus
extern "C"
{
#endif

#ifdef _WIN32
#ifdef VCAL_EXPORTS
#define VCAL_API __declspec(dllexport)
#else
#define VCAL_API __declspec(dllimport)
#endif
#else
#define VCAL_API
#endif
/** define the handle */
#define MOWER_SYSTEM_CALIB_HANDLE void *

  /**
   * @brief 相机内参
   * @param model_          畸变模型
   * @param img_width_pixel      内参对应分辨率宽
   * @param img_height_pixel     内参对应分辨率高
   * @param focal_x_        像素焦距fx
   * @param focal_y_        像素焦距fy
   * @param cx_             主点cx
   * @param cy_             主点cy
   */
  typedef struct
  {
    uint8_t model_;       // 畸变模型：0->K6, 1->KB
    uint32_t img_width_pixel;  // 像素宽1280
    uint32_t img_height_pixel; // 像素高720
    float focal_x_;       // 焦距fx
    float focal_y_;       // 焦距fy
    float cx_;            // 主点cx
    float cy_;            // 主点cy
    float k1_;            // 畸变参数
    float k2_;
    float k3_;
    float k4_;
    float k5_; // 使用KB模型时，k5_=0.0
    float k6_; // 使用KB模型时，k6_=0.0
    float p1_; // 使用KB模型时，p1_=0.0
    float p2_; // 使用KB模型时，p2_=0.0
  } RGBCameraIntrinsic;

  typedef struct
  {
    bool saveResult; // default:1
    int calibBEV;    // default:1 0:不开启标定 1：开启标定
    int calibIMU;    // default:1 0:不开启标定 1:IMU对齐相机 2:IMU对齐mowerfront

  } MowerSystemCalibConfig;

  /**
   * @brief 标定结果
   */
  typedef struct
  {
    bool calib_success; // 最终标定是否成功 0：标定未成功 1：标定成功
    // BEV Result
    int32_t bev_calib_result; //-1:标定失败 #0:不开启标定 1：BEV标定成功

    uint32_t img_width_pixel;        // 像素宽1280
    uint32_t img_height_pixel;       // 像素高720
    float scotoma_distance;    // 车头到bev完整区域最下端的距离（unit:m）
    float bev_physical_width;  // bev区域对应真实物理宽度（unit:m）
    float bev_physical_length; // bev区域对应真实物理长度（unit:m）
    float top_left_x_pixel;       // 左上点 x（unit:pixel）
    float top_left_y_pixel;       // 左上点 y（unit:pixel）
    float bottom_left_x_pixel;    // 左下点 x（unit:pixel）
    float bottom_left_y_pixel;    // 左下点 y（unit:pixel）
    float top_right_x_pixel;      // 右上点 x（unit:pixel）
    float top_right_y_pixel;      // 右上点 y（unit:pixel）
    float bottom_right_x_pixel;   // 右下点 x（unit:pixel）
    float bottom_right_y_pixel;   // 右下点 y（unit:pixel）

    float trans_front_cam_x;  // 相机到mower MowerFront外参x（unit:m）
    float trans_front_cam_y;  // 相机到mower MowerFront外参y（unit:m）
    float trans_front_cam_z;  // 相机到mower MowerFront外参z（unit:m）
    float rot_front_cam_qx; // 相机到mower MowerFront外参qx
    float rot_front_cam_qy; // 相机到mower MowerFront外参qy
    float rot_front_cam_qz; // 相机到mower MowerFront外参qz
    float rot_front_cam_qw; // 相机到mower MowerFront外参qw

    // IMU Result
    int32_t imu_calib_result; // #-1:标定失败 #0:不开启标定 1:IMU对齐相机成功 2:IMU对齐mowerfront成功

    float trans_front_imu_x;  // IMU到mower MowerFront外参x（unit:m）
    float trans_front_imu_y;  // IMU到mower MowerFront外参y（unit:m）
    float trans_front_imu_z;  // IMU到mower MowerFront外参z（unit:m）
    float rot_front_imu_qx; // IMU到mower MowerFront外参qx
    float rot_front_imu_qy; // IMU到mower MowerFront外参qy
    float rot_front_imu_qz; // IMU到mower MowerFront外参qz
    float rot_front_imu_qw; // IMU到mower MowerFront外参qw

    float acc_bias_x; // bias_ax m/s2
    float acc_bias_y; // bias_ay m/s2
    float acc_bias_z; // bias_az m/s2
    float gyr_bias_x; // bias_gx rad/s
    float gyr_bias_y; // bias_gy rad/s
    float gyr_bias_z; // bias_gz rad/s

  } MowerSystemCalibResult;

  /**
   * @brief		  获得版本号
   * @return	  bool
   */
  VCAL_API bool GetVersion_BEVCalib(char version_info[]);

  /**
   * @brief 创建系统
   * @param mower_system_calib_handle 对象句柄
   * @param config_path 配置文件路径及文件名
   * @return 0 for create successfully; RECHARGE_INIT_FAIL for create failure
   */
  VCAL_API ErrCode MSCAL_create(MOWER_SYSTEM_CALIB_HANDLE *mower_system_calib_handle, const char *config_path);

  /**
   * @brief 设置config信息
   * @param mower_system_calib_handle 对象句柄
   * @param mower_sys_calib_config 要设置的信标配置
   * @return 1
   * */
  VCAL_API int32_t MSCAL_set_config_parameter(MOWER_SYSTEM_CALIB_HANDLE mower_system_calib_handle, MowerSystemCalibConfig *mower_sys_calib_config);

  /**
   * @brief 传入图像数据
   * @param mower_system_calib_handle 对象句柄
   * @param width  图像宽像素
   * @param height 图像长像素
   * @param data 图像数据
   * @param cam_intrinsic 相机内参
   * @param camera_flag 相机序号，0：前视，1：左视，2：右视
   * @return ErrCode
   */
  VCAL_API ErrCode MSCAL_add_Image(MOWER_SYSTEM_CALIB_HANDLE mower_system_calib_handle,
                                   const uint32_t width, const uint32_t height, const uint8_t *data,
                                   const RGBCameraIntrinsic *cam_intrinsic,
                                   const uint32_t camera_flag);
  /**
   * @brief 传入IMU数据
   * @param mower_system_calib_handle  对象句柄
   * @param timestamp IMU时间戳（unit:ns）
   * @param ax 加速度计x
   * @param ay 加速度计y
   * @param az 加速度计z
   * @param gx 陀螺仪x
   * @param gy 陀螺仪y
   * @param gz 陀螺仪z
   * @return ErrCode
   */
  VCAL_API ErrCode MSCAL_add_IMU(MOWER_SYSTEM_CALIB_HANDLE mower_system_calib_handle,
                                 const uint64_t timestamp, const double ax, const double ay, const double az, const double gx, const double gy, const double gz);

  /**
   * @brief 进行标定计算
   * @param mower_system_calib_handle  对象句柄
   * @param Calib_result  标定结果返回
   */
  VCAL_API ErrCode MSCAL_calib(MOWER_SYSTEM_CALIB_HANDLE mower_system_calib_handle, MowerSystemCalibResult *Calib_result);

  /**
   * @brief 释放系统
   *
   * @param mower_system_calib_handle 对象句柄
   * @return void
   */
  VCAL_API void MSCAL_release(MOWER_SYSTEM_CALIB_HANDLE mower_system_calib_handle);

  /**
   * @brief		  获得错误信息
   * @return	  void
   */
  VCAL_API void GetCalibErrorInfo(char *error_info);

#ifdef __cplusplus
}
#endif

#endif //__BEVCALIBAPI_H__